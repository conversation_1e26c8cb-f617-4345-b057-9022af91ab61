import { inject } from '@angular/core'
import { Router, ActivatedRouteSnapshot } from '@angular/router'
import {AuthService} from "@/services/auth.service";

export const accessGuard = (route: ActivatedRouteSnapshot) => {
  const router = inject(Router);
  const isLoggedIn = inject(AuthService).isAuth;
  if(isLoggedIn && route.routeConfig?.path == '/login') {
    router.navigate(['/'])
    return false
  }
  if(isLoggedIn) return true;
  return router.navigate(['/login']);
}
