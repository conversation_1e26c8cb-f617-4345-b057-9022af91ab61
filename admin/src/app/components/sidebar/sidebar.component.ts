import { Component } from '@angular/core';
import { NgScrollbar } from 'ngx-scrollbar';
import { IconModule } from "@/shared/icon/icon.module";
import {ActivatedRoute, RouterLink, Router} from "@angular/router";
import {CommonModule} from "@angular/common";

@Component({
    selector: 'sidebar',
    imports: [NgScrollbar, IconModule, RouterLink, CommonModule],
    templateUrl: './sidebar.component.html',
    styleUrl: './sidebar.component.scss'
})
export class SidebarComponent {
  activeDropdown: string[] = [];
  constructor(private router: Router) {}

  isActiveRoute(route: string): boolean {
    return this.router.url.includes(route);
  }
  toggleAccordion(name: string, parent?: string) {
    if (this.activeDropdown.includes(name)) {
      this.activeDropdown = this.activeDropdown.filter((d) => d !== name);
    } else {
      this.activeDropdown.push(name);
    }
  }
}
