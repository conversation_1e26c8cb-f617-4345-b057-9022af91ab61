<div class="sidebar fixed bottom-0 top-0 z-50 h-full min-h-screen w-[260px] shadow-[5px_0_25px_0_rgba(94,92,154,0.1)] transition-all duration-300">
  <div class="h-full">
    <div class="flex items-center justify-center px-4 py-3">
      <a routerLink="/" class="main-logo flex shrink-0 items-center">
        <span class="align-middle text-2xl font-semibold dark:text-white-light lg:inline ltr:ml-1.5 rtl:mr-1.5">ADVAYTA</span>
      </a>
    </div>
    <ng-scrollbar class="relative !h-[calc(100vh-80px)]" appearance="compact">
      <ul class="relative space-y-0.5 p-4 py-0 font-semibold">
        <li class="nav-item">
          <a [class.active-link]="isActiveRoute('content')" routerLink="content" class="group">
            <div class="flex items-center">
              <icon-menu-pages class="shrink-0 group-hover:!text-primary" />
              <span class="text-black dark:text-[#506690] dark:group-hover:text-white-dark ltr:pl-3 rtl:pr-3">
              Страницы
            </span>
            </div>
          </a>
          <a [class.active-link]="isActiveRoute('translations')" routerLink="translations" class="group">
            <div class="flex items-center">
              <icon-menu-forms class="shrink-0 group-hover:!text-primary" />
              <span class="text-black dark:text-[#506690] dark:group-hover:text-white-dark ltr:pl-3 rtl:pr-3">
              Переводы
            </span>
            </div>
          </a>
          <a [class.active-link]="isActiveRoute('library')" routerLink="library" class="group">
            <div class="flex items-center">
              <icon-menu-tables class="shrink-0 group-hover:!text-primary" />
              <span class="text-black dark:text-[#506690] dark:group-hover:text-white-dark ltr:pl-3 rtl:pr-3">
              Книги
            </span>
            </div>
          </a>
          <a [class.active-link]="isActiveRoute('photo')" routerLink="photo" class="group">
            <div class="flex items-center">
              <icon-menu-tables class="shrink-0 group-hover:!text-primary" />
              <span class="text-black dark:text-[#506690] dark:group-hover:text-white-dark ltr:pl-3 rtl:pr-3">
              Фотогалерея
            </span>
            </div>
          </a>
          <a [class.active-link]="isActiveRoute('audio')" routerLink="audio" class="group">
            <div class="flex items-center">
              <icon-menu-tables class="shrink-0 group-hover:!text-primary" />
              <span class="text-black dark:text-[#506690] dark:group-hover:text-white-dark ltr:pl-3 rtl:pr-3">
              Аудиолекции
            </span>
            </div>
          </a>
          <a [class.active-link]="isActiveRoute('constructor')" routerLink="constructor" class="group">
            <div class="flex items-center">
              <icon-menu-tables class="shrink-0 group-hover:!text-primary" />
              <span class="text-black dark:text-[#506690] dark:group-hover:text-white-dark ltr:pl-3 rtl:pr-3">
              Конструктор лендинга
            </span>
            </div>
          </a>
          <a [class.active-link]="isActiveRoute('users')" routerLink="users" class="group">
            <div class="flex items-center">
              <icon-menu-tables class="shrink-0 group-hover:!text-primary" />
              <span class="text-black dark:text-[#506690] dark:group-hover:text-white-dark ltr:pl-3 rtl:pr-3">
              Пользователи
            </span>
            </div>
          </a>
          <a [class.active-link]="isActiveRoute('mypage')" routerLink="mypage" class="group">
            <div class="flex items-center">
              <icon-menu-tables class="shrink-0 group-hover:!text-primary" />
              <span class="text-black dark:text-[#506690] dark:group-hover:text-white-dark ltr:pl-3 rtl:pr-3">
              Личные страницы
            </span>
            </div>
          </a>
        </li>
        <li class="accordion menu nav-item">
          <button
            type="button"
            class="nav-link group w-full"
            [ngClass]="{ active: activeDropdown.includes('forum') }"
            (click)="toggleAccordion('forum')"
          >
            <div class="flex items-center">
              <icon-menu-forms class="shrink-0 group-hover:!text-primary" />

              <span class="text-black dark:text-[#506690] dark:group-hover:text-white-dark ltr:pl-3 rtl:pr-3"
              >Форум</span
              >
            </div>
            <div [ngClass]="{ 'rtl:rotate-90 -rotate-90': !activeDropdown.includes('forum') }">
              <icon-caret-down />
            </div>
          </button>
          <div [ngClass]="{'hidden': !activeDropdown.includes('forum')}" class="accordion-content">
            <ul class="sub-menu text-gray-500">
              <li>
                <a routerLink="/forum/categories" routerLinkActive="active"
                >Категории</a
                >
              </li>
            </ul>
          </div>
        </li>
        <li class="nav-item">
          <a [class.active-link]="isActiveRoute('advertising')" routerLink="advertising" class="group">
            <div class="flex items-center">
              <icon-menu-tables class="shrink-0 group-hover:!text-primary" />
              <span class="text-black dark:text-[#506690] dark:group-hover:text-white-dark ltr:pl-3 rtl:pr-3">
              Календарь
            </span>
            </div>
          </a>
        </li>
        <li class="nav-item">
          <a [class.active-link]="isActiveRoute('audiofiles')" routerLink="audiofiles" class="group">
            <div class="flex items-center">
              <icon-menu-tables class="shrink-0 group-hover:!text-primary" />
              <span class="text-black dark:text-[#506690] dark:group-hover:text-white-dark ltr:pl-3 rtl:pr-3">
              Аудио
            </span>
            </div>
          </a>
        </li>
      </ul>
    </ng-scrollbar>
  </div>
</div>
