import { Routes } from '@angular/router';
import {LoginComponent} from './pages/login/login.component'
import {LayoutComponent} from "./components/layout/layout.component";
import { accessGuard } from './guards/access.guard'
import {ContentComponent} from "@/pages/content/content.component";
import {ContentAddComponent} from "@/pages/content/content-add/content-add.component";
import {CategoryAddComponent} from "@/pages/content/category-add/category-add.component";
import {TranslationsComponent} from "@/pages/translations/translations.component";
import {TranslationsAddComponent} from "@/pages/translations/translations-add/translations-add.component";
import {LibraryComponent} from "@/pages/library/library.component";
import {LibraryAddComponent} from "@/pages/library/library-add/library-add.component";
import {PhotoComponent} from "@/pages/photo/photo.component";
import {PhotoAddComponent} from "@/pages/photo/photo-add/photo-add.component";
import {AudioComponent} from "@/pages/audio/audio.component";
import {ConstructorComponent} from "@/pages/constructor/constructor.component";
import {UsersComponent} from "@/pages/users/users.component";
import {MypageComponent} from "@/pages/mypage/mypage.component";
import {MypageAddComponent} from "@/pages/mypage/mypage-add/mypage-add.component";
import {ForumCategoriesComponent} from "@/pages/forum/forum-categories/forum-categories.component";
import {ForumCategoriesAddComponent} from "@/pages/forum/forum-categories-add/forum-categories-add.component";
import {AdvertisingComponent} from "@/pages/advertising/advertising.component";
import {UserComponent} from "@/pages/users/user/user.component";
import {AudiofilesComponent} from "@/pages/audiofiles/audiofiles.component";
export const routes: Routes = [
    {
      path: '',
      component: LayoutComponent,
      canActivate: [accessGuard],
      children: [
        {
          path: 'content',
          component: ContentComponent,
        },
        {
          path: 'content/add',
          component: ContentAddComponent
        },
        {
          path: 'content/:slug',
          component: ContentAddComponent,
        },
        {
          path: 'category/:id',
          component: CategoryAddComponent
        },
        {
          path: 'translations',
          component: TranslationsComponent,
        },
        {
          path: 'translations/add',
          component: TranslationsAddComponent,
        },
        {
          path: 'translations/:id',
          component: TranslationsAddComponent,
        },
        {
          path: 'library',
          component: LibraryComponent,
        },
        {
          path: 'library/add',
          component: LibraryAddComponent,
        },
        {
          path: 'library/:id',
          component: LibraryAddComponent,
        },
        {
          path: 'photo',
          component: PhotoComponent,
        },
        {
          path: 'photo/add',
          component: PhotoAddComponent,
        },
        {
          path: 'photo/:id',
          component: PhotoAddComponent,
        },
        {
          path: 'audio',
          component: AudioComponent,
        },
        {
          path: 'audio/:page',
          component: AudioComponent,
        },
        {
          path: 'constructor',
          component: ConstructorComponent,
        },
        {
          path: 'users',
          component: UsersComponent,
        },
        {
          path: 'mypage',
          component: MypageComponent,
        },
        {
          path: 'mypage/:id',
          component: MypageAddComponent,
        },
        {
          path: 'forum/categories',
          component: ForumCategoriesComponent
        },
        {
          path: 'forum/categories/add',
          component: ForumCategoriesAddComponent
        },
        {
          path: 'forum/categories/:id',
          component: ForumCategoriesAddComponent
        },
        {
          path: 'advertising',
          component: AdvertisingComponent,
        },
        {
          path: 'users/:id',
          component: UserComponent,
        },
        {
          path: 'audiofiles',
          component: AudiofilesComponent
        },
      ]
    },
    {
      path: 'login',
      component: LoginComponent,
    },
    {
      path: '**',
      redirectTo: () => 'content'
    }
];
