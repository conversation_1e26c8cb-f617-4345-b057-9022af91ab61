import {Injectable, inject, signal} from '@angular/core';
import {HttpClient} from "@angular/common/http";
import {tap} from "rxjs";

interface IProfile {
    name: string | null
}

@Injectable({
  providedIn: 'root'
})
export class UserService {
    http = inject(HttpClient)
    name = signal<string | null>(null)
    users: any = []

    getOne(id: number) {
      return this.http.get('/user/' + id)
    }

    getAll() {
      return this.http.get('/user').pipe(
        tap(res => this.users = res),
      )
    }

    getProfile() {
        return this.http.get<IProfile>('/user/profile').pipe(
            tap(res => this.name.set(res.name))
        )
    }

    getGroups() {
      return this.http.get('/user/groups')
    }

    getStatuses() {
      return this.http.get('/user/statuses')
    }

    updateUser(data: any) {
      return this.http.patch(`/user/profile`, data)
    }

    deleteUser(id: number) {
      return this.http.delete(`/user/${id}`)
    }
}
