<div class="search">
  <div class="search-wrap" [ngClass]="{'empty': !items}">
    <!-- <div class="flex flex-col">


      <div class="flex justify-between mt-8">
        <input [(ngModel)]="query" class="form-input" type="text" placeholder="Поисковой запрос" (keyup.enter)="search()">
        <button class="btn-search" (click)="search()">
           Поиск
        </button>
        @if(totalItems > 0) {
          <div class="flex rounded-full items-center justify-center w-10 h-10 absolute right-10" style="color: white;">
            <span class="h-[30px]">{{totalItems}}</span>
          </div>
        }
      </div>
    </div> -->
    <div class="dec_head _background">
      <!-- <img class="carn_b_" width="862" height="84" alt="icon" ngSrc="assets/images/top_drawing.svg" /> -->
      <!-- <h1 class="dec_head-title_">Search</h1> -->
      <!-- <img class="carn_b_ rotate" width="862" height="84" alt="icon" ngSrc="assets/images/top_drawing.svg" /> -->
    </div>
    <div class="articles-search relative">
      <input [(ngModel)]="query" type="text" placeholder="Поиск" (keyup.enter)="search()">
      <div class="articles-search-btn_" (click)="search()">
      </div>
    </div>
    <ng-container *ngIf="!items">
      <div class="flex flex-col justify-center">
        <p class="e_search">
          Ничего не найдено, все состоит из пустоты!
        </p>
        <div class="e_wrap">
          <p class="e_text">Переформулируйте запрос или напишите что нибудь еще.</p>
          <div class="audio_chip">
            <span>
              <svg width="17" height="18" viewBox="0 0 17 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M16 16.5L12.3808 12.8808M12.3808 12.8808C13.5872 11.6743 14.3333 10.0076 14.3333 8.16667C14.3333 4.48477 11.3486 1.5 7.66667 1.5C3.98477 1.5 1 4.48477 1 8.16667C1 11.8486 3.98477 14.8333 7.66667 14.8333C9.50758 14.8333 11.1743 14.0872 12.3808 12.8808Z"
                  stroke="var(--book_about)" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
              </svg>
            </span>
            Практика Атма-вичары
          </div>
          <div class="audio_chip">
            <span>
              <svg width="17" height="18" viewBox="0 0 17 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M16 16.5L12.3808 12.8808M12.3808 12.8808C13.5872 11.6743 14.3333 10.0076 14.3333 8.16667C14.3333 4.48477 11.3486 1.5 7.66667 1.5C3.98477 1.5 1 4.48477 1 8.16667C1 11.8486 3.98477 14.8333 7.66667 14.8333C9.50758 14.8333 11.1743 14.0872 12.3808 12.8808Z"
                  stroke="var(--book_about)" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
              </svg>
            </span>
            Полнота и подлинность духовной традиции
          </div>
          <div class="audio_chip">
            <span>
              <svg width="17" height="18" viewBox="0 0 17 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M16 16.5L12.3808 12.8808M12.3808 12.8808C13.5872 11.6743 14.3333 10.0076 14.3333 8.16667C14.3333 4.48477 11.3486 1.5 7.66667 1.5C3.98477 1.5 1 4.48477 1 8.16667C1 11.8486 3.98477 14.8333 7.66667 14.8333C9.50758 14.8333 11.1743 14.0872 12.3808 12.8808Z"
                  stroke="var(--book_about)" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
              </svg>
            </span>
            К чему снится ободранная собака?
          </div>
        </div>
      </div>
    </ng-container>
    <ng-container *ngIf="items">
      <div class="search-tabs">
        <div class="v-divider"></div>
        <ng-container *ngFor="let tab of tabs">
          <div class="search-tab" [ngClass]="{'active': activeTab == tab}"
            (click)="activeTab = tab; page = 1; search(true)">{{tab}}</div>
          <div class="v-divider"></div>
        </ng-container>
      </div>
      <div class="mobile-search-tabs justify-between items-center">
        <div class="prev-button" [ngClass]="{'cursor-pointer': tabs[0] !== activeTab}">
          <img src="../../../assets/images/icons/arrow-in-circle.svg" alt="prev" (click)="showPrev()">
        </div>
        <div class="search-tab active">{{activeTab}}</div>
        <div class="next-button" [ngClass]="{'cursor-pointer': tabs[tabs.length-1] !== activeTab}">
          <img src="../../../assets/images/icons/arrow-in-circle.svg" alt="next" (click)="showNext()">
        </div>
      </div>

      <div class="search-tabs__content" *ngIf="items">
        <!-- All Results Tab -->
        <div class="search-content flex flex-col" *ngIf="activeTab === 'Все'">
          @for(item of items; track item.id; let i = $index; let last = $last) {
          <div *ngIf="item.type != 'photo' && item.type != 'forum'">
            <!-- <div class="search-label">
              {{labels[item.key]}}
            </div> -->
            @if (item.type == 'audio') {
            <div>
              <div [ngClass]="{'widen': isOpened__[i], 'last': last}" class="article-item relative">
                <div class="vis_part">
                  <div (click)="isOpened__[i] = !isOpened__[i]" class="art_img_"></div>
                  <div (click)="playTrack(item); $event.stopPropagation();" class="art_img">
                    @if(true) {
                    <img src="assets/images/icons/play_lst.svg" alt="play">
                    } @else {
                    <img src="assets/images/icons/pause_lst.svg" alt="pause">
                    }
                  </div>
                  <div (click)="isOpened__[i] = !isOpened__[i]" class="flex justify-between sp_width">
                    @if(item){
                    <div class="titl_w w-full">
                      <div class="article-title" (click)="openLecture(item.external_id)">
                        {{item.title}}
                      </div>
                      <div class="article-category">
                        {{ item.authorName || item.author }}</div>
                      <div class="flex items-center icons_w b_p">
                        <div class="flex items-center">
                          <div class="icon-wrap cal_w">
                            <svg width="22" height="24" viewBox="0 0 22 24" fill="none"
                              xmlns="http://www.w3.org/2000/svg">
                              <path
                                d="M6.41826 5.02326C5.96059 5.02326 5.58105 4.64372 5.58105 4.18605V0.837209C5.58105 0.379535 5.96059 0 6.41826 0C6.87594 0 7.25547 0.379535 7.25547 0.837209V4.18605C7.25547 4.64372 6.87594 5.02326 6.41826 5.02326Z"
                                fill="var(--text-color)" />
                              <path
                                d="M15.3489 5.02326C14.8913 5.02326 14.5117 4.64372 14.5117 4.18605V0.837209C14.5117 0.379535 14.8913 0 15.3489 0C15.8066 0 16.1861 0.379535 16.1861 0.837209V4.18605C16.1861 4.64372 15.8066 5.02326 15.3489 5.02326Z"
                                fill="var(--text-color)" />
                              <path
                                d="M6.97663 14.7908C6.83151 14.7908 6.6864 14.7573 6.55244 14.7015C6.40733 14.6457 6.2957 14.5676 6.18407 14.4671C5.98314 14.255 5.86035 13.9759 5.86035 13.6745C5.86035 13.5294 5.89384 13.3843 5.94965 13.2503C6.00547 13.1164 6.08361 12.9936 6.18407 12.882C6.2957 12.7815 6.40733 12.7034 6.55244 12.6476C6.9543 12.4801 7.45663 12.5694 7.76919 12.882C7.97012 13.0941 8.09291 13.3843 8.09291 13.6745C8.09291 13.7415 8.08175 13.8196 8.07058 13.8978C8.05942 13.9648 8.0371 14.0317 8.00361 14.0987C7.98128 14.1657 7.94779 14.2327 7.90314 14.2996C7.86965 14.3555 7.81384 14.4113 7.76919 14.4671C7.5571 14.668 7.26686 14.7908 6.97663 14.7908Z"
                                fill="var(--text-color)" />
                              <path
                                d="M10.8839 14.7906C10.7387 14.7906 10.5936 14.7572 10.4597 14.7013C10.3146 14.6455 10.2029 14.5674 10.0913 14.4669C9.89037 14.2548 9.76758 13.9758 9.76758 13.6744C9.76758 13.5292 9.80107 13.3841 9.85688 13.2502C9.91269 13.1162 9.99083 12.9934 10.0913 12.8818C10.2029 12.7813 10.3146 12.7032 10.4597 12.6474C10.8615 12.4688 11.3639 12.5692 11.6764 12.8818C11.8773 13.0939 12.0001 13.3841 12.0001 13.6744C12.0001 13.7413 11.989 13.8195 11.9778 13.8976C11.9666 13.9646 11.9443 14.0316 11.9108 14.0986C11.8885 14.1655 11.855 14.2325 11.8104 14.2995C11.7769 14.3553 11.7211 14.4111 11.6764 14.4669C11.4643 14.6679 11.1741 14.7906 10.8839 14.7906Z"
                                fill="var(--text-color)" />
                              <path
                                d="M14.7911 14.7906C14.646 14.7906 14.5009 14.7572 14.3669 14.7013C14.2218 14.6455 14.1102 14.5674 13.9985 14.4669C13.9539 14.4111 13.9092 14.3553 13.8646 14.2995C13.8199 14.2325 13.7864 14.1655 13.7641 14.0986C13.7306 14.0316 13.7083 13.9646 13.6971 13.8976C13.686 13.8195 13.6748 13.7413 13.6748 13.6744C13.6748 13.3841 13.7976 13.0939 13.9985 12.8818C14.1102 12.7813 14.2218 12.7032 14.3669 12.6474C14.7799 12.4688 15.2711 12.5692 15.5836 12.8818C15.7846 13.0939 15.9074 13.3841 15.9074 13.6744C15.9074 13.7413 15.8962 13.8195 15.885 13.8976C15.8739 13.9646 15.8515 14.0316 15.8181 14.0986C15.7957 14.1655 15.7622 14.2325 15.7176 14.2995C15.6841 14.3553 15.6283 14.4111 15.5836 14.4669C15.3715 14.6679 15.0813 14.7906 14.7911 14.7906Z"
                                fill="var(--text-color)" />
                              <path
                                d="M6.97663 18.6976C6.83151 18.6976 6.6864 18.6642 6.55244 18.6084C6.41849 18.5526 6.2957 18.4744 6.18407 18.3739C5.98314 18.1618 5.86035 17.8716 5.86035 17.5813C5.86035 17.4362 5.89384 17.2911 5.94965 17.1572C6.00547 17.012 6.08361 16.8894 6.18407 16.7889C6.5971 16.3759 7.35617 16.3759 7.76919 16.7889C7.97012 17.001 8.09291 17.2911 8.09291 17.5813C8.09291 17.8716 7.97012 18.1618 7.76919 18.3739C7.5571 18.5748 7.26686 18.6976 6.97663 18.6976Z"
                                fill="var(--text-color)" />
                              <path
                                d="M10.8839 18.6976C10.5936 18.6976 10.3034 18.5748 10.0913 18.3739C9.89037 18.1618 9.76758 17.8716 9.76758 17.5813C9.76758 17.4362 9.80107 17.2911 9.85688 17.1572C9.91269 17.012 9.99083 16.8894 10.0913 16.7889C10.5043 16.3759 11.2634 16.3759 11.6764 16.7889C11.7769 16.8894 11.855 17.012 11.9108 17.1572C11.9666 17.2911 12.0001 17.4362 12.0001 17.5813C12.0001 17.8716 11.8773 18.1618 11.6764 18.3739C11.4643 18.5748 11.1741 18.6976 10.8839 18.6976Z"
                                fill="var(--text-color)" />
                              <path
                                d="M14.7911 18.6975C14.5009 18.6975 14.2106 18.5747 13.9985 18.3738C13.8981 18.2733 13.8199 18.1506 13.7641 18.0054C13.7083 17.8715 13.6748 17.7264 13.6748 17.5813C13.6748 17.4361 13.7083 17.291 13.7641 17.1571C13.8199 17.0119 13.8981 16.8892 13.9985 16.7887C14.2553 16.5319 14.646 16.4092 15.0032 16.4873C15.0813 16.4985 15.1483 16.5208 15.2153 16.5543C15.2822 16.5766 15.3492 16.6101 15.4162 16.6547C15.472 16.6882 15.5278 16.744 15.5836 16.7887C15.7846 17.0008 15.9074 17.291 15.9074 17.5813C15.9074 17.8715 15.7846 18.1617 15.5836 18.3738C15.3715 18.5747 15.0813 18.6975 14.7911 18.6975Z"
                                fill="var(--text-color)" />
                              <path
                                d="M20.3716 9.5886H1.39483C0.937152 9.5886 0.557617 9.20907 0.557617 8.75139C0.557617 8.29372 0.937152 7.91418 1.39483 7.91418H20.3716C20.8292 7.91418 21.2088 8.29372 21.2088 8.75139C21.2088 9.20907 20.8292 9.5886 20.3716 9.5886Z"
                                fill="var(--text-color)" />
                              <path
                                d="M15.3488 24H6.4186C2.34419 24 0 21.6558 0 17.5814V8.09304C0 4.01862 2.34419 1.67444 6.4186 1.67444H15.3488C19.4233 1.67444 21.7674 4.01862 21.7674 8.09304V17.5814C21.7674 21.6558 19.4233 24 15.3488 24ZM6.4186 3.34886C3.22605 3.34886 1.67442 4.90049 1.67442 8.09304V17.5814C1.67442 20.774 3.22605 22.3256 6.4186 22.3256H15.3488C18.5414 22.3256 20.093 20.774 20.093 17.5814V8.09304C20.093 4.90049 18.5414 3.34886 15.3488 3.34886H6.4186Z"
                                fill="var(--text-color)" />
                            </svg>
                          </div>
                          <span class="ml-2 text-color">
                            {{ item.date }}
                          </span>
                        </div>
                        <div class="audio-views flex items-center ml-[30px]">
                          <img class="mr-2" src="assets/images/icons/chck.svg" alt="">
                          <span class="ml-1 text-color">
                            прослушана {{item.views}} раз (a)
                          </span>
                        </div>
                      </div>
                    </div>
                    }
                    <div class="actions_w" (click)="showMenu(i, $event)" [class.show_menu]="show_menu[i]">
                      <div class="flex items-center cursor-pointer icons_w show_md">
                        <div class="icon-wrap star_w no_h">
                          <svg width="7" height="22" viewBox="0 0 7 22" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <circle cx="3.27778" cy="3.27778" r="2.27778" fill="#DEA53D" stroke="#DEA53D" />
                            <circle cx="3.27778" cy="11.2499" r="2.27778" fill="#DEA53D" stroke="#DEA53D" />
                            <circle cx="3.27778" cy="19.2221" r="2.27778" fill="#DEA53D" stroke="#DEA53D" />
                          </svg>
                        </div>
                      </div>
                      <div class="md_chg">
                        <div title="Добавить в очередь" class="flex items-center cursor-pointer icons_w fav_hov"
                          [ngClass]="{'in-favourites': item.id | isInPlaylist : playlists}"
                          (click)="addToPlaylist(item); $event.stopPropagation();">
                          <div class="icon-wrap">
                            <img class="list_n" src="assets/images/icons/list_n.svg" alt="quee">
                          </div>
                          <p class="show_p_md">
                            добавить в очередь
                          </p>
                          <div class="on_hov">
                            добавить в очередь
                          </div>
                        </div>
                        <div class="flex items-center cursor-pointer icons_w fav_hov"
                          [ngClass]="{'in-favourites': item.id | isInPlaylist : playlists}"
                          (click)="showPlaylistDialog(item.id); $event.stopPropagation();">
                          <div class="icon-wrap star_w">
                            <svg class="emty_f" width="25" height="24" viewBox="0 0 25 24" fill="none"
                              xmlns="http://www.w3.org/2000/svg">
                              <path
                                d="M5.96868 23.9618C5.94253 23.9618 5.91638 23.9618 5.89022 23.9618C5.36715 23.9356 4.89638 23.6999 4.53023 23.3333C4.16408 22.9143 3.98101 22.3906 4.03332 21.8145C4.05947 21.5789 4.11178 21.317 4.19024 21.0552C4.71331 19.4055 5.41946 17.2844 6.1256 15.1372C4.42562 13.9065 2.72564 12.6758 1.02565 11.4189C0.816424 11.2618 0.162584 10.7904 0.0318159 9.95249C-0.0727985 9.35022 0.0841231 8.77414 0.450273 8.35517C0.842577 7.88383 1.44411 7.62197 2.15026 7.62197C2.90871 7.62197 3.66717 7.62197 4.42562 7.62197H8.55789C8.68866 7.25537 8.79327 6.88878 8.92404 6.52218C9.08096 6.05084 9.21173 5.60568 9.36865 5.13435C9.7348 4.00837 10.0748 2.8562 10.4671 1.73022C10.5979 1.31125 10.8333 0.918472 11.1209 0.604245C11.4871 0.211462 12.0363 -0.0242083 12.5855 0.00197721C13.1348 0.0281628 13.6578 0.290018 14.024 0.735173C14.2332 1.02321 14.4163 1.33744 14.5471 1.70404C15.044 3.24899 15.5409 4.79393 16.0378 6.33888L16.4563 7.62197H17.6593C19.3332 7.62197 21.007 7.62197 22.6808 7.62197C23.047 7.62197 23.387 7.67434 23.7008 7.77909C24.2762 7.98857 24.7208 8.43372 24.9039 9.00981C25.087 9.58589 25.0085 10.1882 24.6685 10.7119C24.4854 10.9999 24.25 11.2356 23.9624 11.4451C22.2624 12.6758 20.5624 13.9327 18.8886 15.1372C19.5424 17.1273 20.1962 19.0912 20.8239 21.0813C21.0593 21.7884 21.0332 22.3906 20.7455 22.9405C20.327 23.7785 19.3593 24.1974 18.4701 23.9094C18.1301 23.8046 17.8163 23.6475 17.5286 23.438C16.1424 22.443 14.7563 21.4218 13.344 20.4005L12.5071 19.7983L11.5132 20.5314C10.1794 21.5003 8.84558 22.4954 7.51174 23.4642C6.96252 23.7785 6.4656 23.9618 5.96868 23.9618ZM3.45794 9.14073C3.01333 9.14073 2.59487 9.14073 2.15026 9.14073C1.91487 9.14073 1.7318 9.21929 1.65334 9.32403C1.57488 9.40259 1.54872 9.53352 1.57488 9.66445C1.60103 9.76919 1.65334 9.90012 1.94103 10.1096C3.71947 11.3927 5.47176 12.702 7.25021 13.9851C7.69482 14.3255 7.85174 14.7968 7.69482 15.3205C6.96252 17.5463 6.23022 19.7983 5.68099 21.5003C5.62869 21.6312 5.60253 21.7884 5.60253 21.8931C5.60253 22.0502 5.62869 22.155 5.70715 22.2597C5.75945 22.3383 5.86407 22.3644 5.94253 22.3644C6.0733 22.3644 6.23022 22.3383 6.54406 22.1026C7.87789 21.1337 9.21173 20.1387 10.5456 19.1698L12.4809 17.7558L14.2594 19.0389C15.6455 20.0601 17.0317 21.0552 18.4178 22.0764C18.5747 22.1811 18.7316 22.2597 18.9147 22.3121C19.0716 22.3644 19.2286 22.2859 19.307 22.1288C19.3593 22.024 19.4116 21.8407 19.2809 21.4741C18.6009 19.4055 17.9209 17.3368 17.2409 15.2682C17.0578 14.7183 17.2147 14.2731 17.6593 13.9327C19.4116 12.6758 21.1901 11.3665 22.9424 10.0834C23.0731 9.97867 23.1777 9.87393 23.2824 9.743C23.3608 9.63826 23.387 9.50733 23.3347 9.40259C23.3085 9.29785 23.2039 9.21929 23.0993 9.16692C22.9685 9.11455 22.7854 9.08836 22.6024 9.08836C20.9285 9.08836 19.2547 9.08836 17.5809 9.08836H15.2794L15.0963 8.59084C15.0701 8.51228 15.044 8.45991 15.0178 8.40754L14.4948 6.78404C13.9978 5.23909 13.5009 3.69414 13.004 2.14919C12.9517 1.96589 12.8471 1.78259 12.7425 1.65167C12.664 1.54692 12.5332 1.52074 12.4809 1.52074C12.4286 1.52074 12.324 1.52074 12.2455 1.62548C12.1148 1.75641 12.0102 1.96589 11.9317 2.17538C11.5656 3.30136 11.1994 4.45352 10.8333 5.55331C10.6763 6.02465 10.5456 6.46981 10.3886 6.94115C10.2579 7.30775 10.1533 7.70053 10.0225 8.06713L9.68249 9.14073H4.42562C4.11178 9.14073 3.77178 9.14073 3.45794 9.14073Z"
                                fill="var(--font-color1)" />
                            </svg>
                            <svg class="emty_f_hover" width="25" height="24" viewBox="0 0 25 24" fill="none"
                              xmlns="http://www.w3.org/2000/svg">
                              <path
                                d="M5.96868 23.9618C5.94253 23.9618 5.91638 23.9618 5.89022 23.9618C5.36715 23.9356 4.89638 23.6999 4.53023 23.3333C4.16408 22.9143 3.98101 22.3906 4.03332 21.8145C4.05947 21.5789 4.11178 21.317 4.19024 21.0552C4.71331 19.4055 5.41946 17.2844 6.1256 15.1372C4.42562 13.9065 2.72564 12.6758 1.02565 11.4189C0.816424 11.2618 0.162584 10.7904 0.0318159 9.95249C-0.0727985 9.35022 0.0841231 8.77414 0.450273 8.35517C0.842577 7.88383 1.44411 7.62197 2.15026 7.62197C2.90871 7.62197 3.66717 7.62197 4.42562 7.62197H8.55789C8.68866 7.25537 8.79327 6.88878 8.92404 6.52218C9.08096 6.05084 9.21173 5.60568 9.36865 5.13435C9.7348 4.00837 10.0748 2.8562 10.4671 1.73022C10.5979 1.31125 10.8333 0.918472 11.1209 0.604245C11.4871 0.211462 12.0363 -0.0242083 12.5855 0.00197721C13.1348 0.0281628 13.6578 0.290018 14.024 0.735173C14.2332 1.02321 14.4163 1.33744 14.5471 1.70404C15.044 3.24899 15.5409 4.79393 16.0378 6.33888L16.4563 7.62197H17.6593C19.3332 7.62197 21.007 7.62197 22.6808 7.62197C23.047 7.62197 23.387 7.67434 23.7008 7.77909C24.2762 7.98857 24.7208 8.43372 24.9039 9.00981C25.087 9.58589 25.0085 10.1882 24.6685 10.7119C24.4854 10.9999 24.25 11.2356 23.9624 11.4451C22.2624 12.6758 20.5624 13.9327 18.8886 15.1372C19.5424 17.1273 20.1962 19.0912 20.8239 21.0813C21.0593 21.7884 21.0332 22.3906 20.7455 22.9405C20.327 23.7785 19.3593 24.1974 18.4701 23.9094C18.1301 23.8046 17.8163 23.6475 17.5286 23.438C16.1424 22.443 14.7563 21.4218 13.344 20.4005L12.5071 19.7983L11.5132 20.5314C10.1794 21.5003 8.84558 22.4954 7.51174 23.4642C6.96252 23.7785 6.4656 23.9618 5.96868 23.9618ZM3.45794 9.14073C3.01333 9.14073 2.59487 9.14073 2.15026 9.14073C1.91487 9.14073 1.7318 9.21929 1.65334 9.32403C1.57488 9.40259 1.54872 9.53352 1.57488 9.66445C1.60103 9.76919 1.65334 9.90012 1.94103 10.1096C3.71947 11.3927 5.47176 12.702 7.25021 13.9851C7.69482 14.3255 7.85174 14.7968 7.69482 15.3205C6.96252 17.5463 6.23022 19.7983 5.68099 21.5003C5.62869 21.6312 5.60253 21.7884 5.60253 21.8931C5.60253 22.0502 5.62869 22.155 5.70715 22.2597C5.75945 22.3383 5.86407 22.3644 5.94253 22.3644C6.0733 22.3644 6.23022 22.3383 6.54406 22.1026C7.87789 21.1337 9.21173 20.1387 10.5456 19.1698L12.4809 17.7558L14.2594 19.0389C15.6455 20.0601 17.0317 21.0552 18.4178 22.0764C18.5747 22.1811 18.7316 22.2597 18.9147 22.3121C19.0716 22.3644 19.2286 22.2859 19.307 22.1288C19.3593 22.024 19.4116 21.8407 19.2809 21.4741C18.6009 19.4055 17.9209 17.3368 17.2409 15.2682C17.0578 14.7183 17.2147 14.2731 17.6593 13.9327C19.4116 12.6758 21.1901 11.3665 22.9424 10.0834C23.0731 9.97867 23.1777 9.87393 23.2824 9.743C23.3608 9.63826 23.387 9.50733 23.3347 9.40259C23.3085 9.29785 23.2039 9.21929 23.0993 9.16692C22.9685 9.11455 22.7854 9.08836 22.6024 9.08836C20.9285 9.08836 19.2547 9.08836 17.5809 9.08836H15.2794L15.0963 8.59084C15.0701 8.51228 15.044 8.45991 15.0178 8.40754L14.4948 6.78404C13.9978 5.23909 13.5009 3.69414 13.004 2.14919C12.9517 1.96589 12.8471 1.78259 12.7425 1.65167C12.664 1.54692 12.5332 1.52074 12.4809 1.52074C12.4286 1.52074 12.324 1.52074 12.2455 1.62548C12.1148 1.75641 12.0102 1.96589 11.9317 2.17538C11.5656 3.30136 11.1994 4.45352 10.8333 5.55331C10.6763 6.02465 10.5456 6.46981 10.3886 6.94115C10.2579 7.30775 10.1533 7.70053 10.0225 8.06713L9.68249 9.14073H4.42562C4.11178 9.14073 3.77178 9.14073 3.45794 9.14073Z"
                                fill="var(--text-color)" />
                            </svg>
                          </div>
                          <p class="show_p_md">
                            добавить в избранное
                          </p>
                          <div class="on_hov">
                            добавить в избранное
                          </div>
                        </div>
                        <div class="flex items-center cursor-pointer icons_w shr_hov"
                          (click)="shareTrack(item); $event.stopPropagation();">
                          <div class="icon-wrap share_w">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                              xmlns="http://www.w3.org/2000/svg">
                              <path
                                d="M15.9238 7.01009C13.5066 8.21941 11.0965 9.42157 8.66504 10.638C8.9368 11.5325 8.94395 12.427 8.66504 13.3357C11.0822 14.5451 13.4923 15.7544 15.8737 16.9422C16.2456 16.5988 16.5745 16.2267 16.9607 15.9476C19.6353 13.9941 23.4542 15.6113 23.9334 18.8957C24.3481 21.7509 22.0168 24.2268 19.1347 23.9835C17.0894 23.8117 15.3731 22.1516 15.1514 20.1051C15.1085 19.6829 15.1156 19.2464 15.1729 18.8313C15.2086 18.588 15.1442 18.5093 14.9512 18.4163C12.8772 17.3859 10.8105 16.3483 8.73656 15.3107C8.50056 15.1891 8.25741 15.0817 8.02141 14.9458C7.87838 14.8671 7.79972 14.8885 7.6853 15.0102C6.76276 15.9834 5.63283 16.4556 4.28836 16.4413C2.21445 16.4127 0.326467 14.7454 0.0475605 12.6846C-0.288557 10.2588 1.18464 8.12638 3.58752 7.63979C5.16084 7.32494 6.52676 7.79722 7.65669 8.94213C7.80687 9.09241 7.89984 9.10672 8.07862 9.01369C10.3742 7.85447 12.677 6.70955 14.9798 5.56464C15.1585 5.47877 15.2158 5.40005 15.1729 5.18538C14.7295 2.75959 16.4458 0.39105 18.8773 0.047575C21.4232 -0.317367 23.7117 1.45725 23.9763 3.99753C24.2266 6.37323 22.4673 8.57004 20.093 8.8348C18.4196 9.028 17.0751 8.41977 16.0239 7.12458C15.9881 7.09596 15.9595 7.06018 15.9238 7.01009ZM7.14179 11.9976C7.14179 10.5021 5.91174 9.26414 4.42424 9.2713C2.94389 9.27845 1.721 10.4949 1.70669 11.9762C1.69239 13.4574 2.92244 14.7025 4.41709 14.7168C5.90459 14.7311 7.14179 13.5003 7.14179 11.9976ZM16.832 19.5541C16.8248 21.0496 18.0549 22.2876 19.5424 22.2804C21.0227 22.2804 22.2671 21.0353 22.2671 19.5541C22.2599 18.0728 21.0513 16.8635 19.5567 16.8492C18.062 16.8277 16.8391 18.0442 16.832 19.5541ZM19.5424 7.14605C21.037 7.14605 22.2671 5.91527 22.2671 4.42688C22.2599 2.93849 21.0156 1.70055 19.5352 1.7077C18.0477 1.71486 16.8463 2.93133 16.832 4.41972C16.8248 5.92242 18.0406 7.14605 19.5424 7.14605Z"
                                fill="var(--font-color1)" />
                            </svg>
                          </div>
                          <p class="show_p_md">
                            поделиться
                          </p>
                          <div class="on_hov">
                            поделиться
                          </div>
                        </div>
                        <button class="flex items-center icons_w lik_hov" [ngClass]="{'is-liked': item.liked}"
                          (click)="likeTrack(item); $event.stopPropagation();">
                          <div class="icon-wrap like_w">
                            <svg class="emty_l" width="24" height="22" viewBox="0 0 24 22" fill="none"
                              xmlns="http://www.w3.org/2000/svg">
                              <path
                                d="M11.7534 2.35249C11.576 2.19912 11.4123 2.05272 11.2486 1.89935C10.048 0.749078 8.90875 0.163484 7.67404 0.0589139C7.44892 0.0379998 7.22381 0.0310285 7.00552 0.0310285C4.87718 0.0310285 3.11038 0.909419 1.72559 2.65923C0.286236 4.47875 -0.252671 6.65381 0.108874 9.12864C0.402203 11.1294 1.2549 12.7816 2.63969 14.0365C4.74757 15.9397 6.91684 17.8428 9.01789 19.6763C9.79555 20.3595 10.5732 21.0357 11.3509 21.7189C11.4668 21.8235 11.5896 21.9002 11.7056 21.942C12.0399 22.0675 12.3468 21.9908 12.6674 21.698C13.8612 20.6453 15.055 19.5926 16.2556 18.54C17.0742 17.815 17.8996 17.0969 18.7182 16.3719C19.0047 16.1209 19.2981 15.8769 19.5777 15.6329C20.2258 15.0822 20.8943 14.5175 21.4946 13.8971C23.6229 11.6802 24.4006 9.03105 23.8071 6.03336C23.3023 3.49579 21.9517 1.69718 19.796 0.679364C17.7291 -0.296625 15.7713 -0.21994 13.9908 0.902448C13.3223 1.32073 12.7016 1.87844 12.1422 2.55466L12.074 2.63831L11.9989 2.5686C11.9171 2.49192 11.8352 2.4222 11.7534 2.35249ZM12.7561 4.47875C12.8175 4.38812 12.8789 4.2975 12.9403 4.20687C13.1586 3.87922 13.3837 3.54459 13.6566 3.25877C15.13 1.69021 17.4426 1.39044 19.4072 2.52677C20.8602 3.36334 21.7538 4.69486 22.0744 6.47953C22.5315 9.0659 21.7675 11.2689 19.7892 13.0187C18.0224 14.5802 16.2215 16.1697 14.4752 17.6964C13.7316 18.3517 12.9881 19.0001 12.2513 19.6554C12.2172 19.6902 12.1763 19.7181 12.1354 19.753L11.9989 19.8645L8.01512 16.3579C7.59218 15.9815 7.15559 15.605 6.73265 15.2425C5.79809 14.4338 4.82943 13.5973 3.90851 12.7328C2.5783 11.485 1.8211 9.77698 1.78699 7.92957C1.75288 6.17976 2.37365 4.51361 3.49921 3.36334C5.02043 1.80872 7.43528 1.39741 9.23618 2.38037C9.94563 2.77077 10.5459 3.35636 11.0576 4.17201C11.0848 4.21384 11.1121 4.25567 11.1326 4.2975C11.1667 4.36024 11.2008 4.41601 11.2417 4.47178C11.4191 4.73669 11.692 4.89703 11.9853 4.89703H11.9921C12.2923 4.904 12.5719 4.75063 12.7561 4.47875Z"
                                fill="var(--font-color1)" />
                            </svg>
                            <svg class="emty_l_hover" width="24" height="22" viewBox="0 0 24 22" fill="none"
                              xmlns="http://www.w3.org/2000/svg">
                              <path
                                d="M11.7534 2.35249C11.576 2.19912 11.4123 2.05272 11.2486 1.89935C10.048 0.749078 8.90875 0.163484 7.67404 0.0589139C7.44892 0.0379998 7.22381 0.0310285 7.00552 0.0310285C4.87718 0.0310285 3.11038 0.909419 1.72559 2.65923C0.286236 4.47875 -0.252671 6.65381 0.108874 9.12864C0.402203 11.1294 1.2549 12.7816 2.63969 14.0365C4.74757 15.9397 6.91684 17.8428 9.01789 19.6763C9.79555 20.3595 10.5732 21.0357 11.3509 21.7189C11.4668 21.8235 11.5896 21.9002 11.7056 21.942C12.0399 22.0675 12.3468 21.9908 12.6674 21.698C13.8612 20.6453 15.055 19.5926 16.2556 18.54C17.0742 17.815 17.8996 17.0969 18.7182 16.3719C19.0047 16.1209 19.2981 15.8769 19.5777 15.6329C20.2258 15.0822 20.8943 14.5175 21.4946 13.8971C23.6229 11.6802 24.4006 9.03105 23.8071 6.03336C23.3023 3.49579 21.9517 1.69718 19.796 0.679364C17.7291 -0.296625 15.7713 -0.21994 13.9908 0.902448C13.3223 1.32073 12.7016 1.87844 12.1422 2.55466L12.074 2.63831L11.9989 2.5686C11.9171 2.49192 11.8352 2.4222 11.7534 2.35249ZM12.7561 4.47875C12.8175 4.38812 12.8789 4.2975 12.9403 4.20687C13.1586 3.87922 13.3837 3.54459 13.6566 3.25877C15.13 1.69021 17.4426 1.39044 19.4072 2.52677C20.8602 3.36334 21.7538 4.69486 22.0744 6.47953C22.5315 9.0659 21.7675 11.2689 19.7892 13.0187C18.0224 14.5802 16.2215 16.1697 14.4752 17.6964C13.7316 18.3517 12.9881 19.0001 12.2513 19.6554C12.2172 19.6902 12.1763 19.7181 12.1354 19.753L11.9989 19.8645L8.01512 16.3579C7.59218 15.9815 7.15559 15.605 6.73265 15.2425C5.79809 14.4338 4.82943 13.5973 3.90851 12.7328C2.5783 11.485 1.8211 9.77698 1.78699 7.92957C1.75288 6.17976 2.37365 4.51361 3.49921 3.36334C5.02043 1.80872 7.43528 1.39741 9.23618 2.38037C9.94563 2.77077 10.5459 3.35636 11.0576 4.17201C11.0848 4.21384 11.1121 4.25567 11.1326 4.2975C11.1667 4.36024 11.2008 4.41601 11.2417 4.47178C11.4191 4.73669 11.692 4.89703 11.9853 4.89703H11.9921C12.2923 4.904 12.5719 4.75063 12.7561 4.47875Z"
                                fill="var(--text-color)" />
                            </svg>
                          </div>
                          <p class="show_p_md">
                            мне нравится
                          </p>
                          <span class="ml-2 text-color default_">
                            {{item.likes}}
                          </span>
                          <div class="on_hov">
                            мне нравится
                          </div>
                        </button>
                      </div>
                    </div>
                  </div>
                  <div class="flex bs_wrapper_">
                    @if(item.text_link) {
                    <p (click)="openLecture(item.external_id)" class="btn_item_wrapper">
                      <span class="btn_item_">Читать</span>
                    </p>
                    }
                    @if(item.youtube) {
                    <p (click)="openLecture(item.external_id)" class="btn_item_wrapper">
                      <span class="btn_item_">Смотреть</span>
                    </p>
                    }
                  </div>
                </div>
                <div class="flex bs_wrapper_ vis_md">
                  @if(item.text_link) {
                  <p (click)="openLecture(item.external_id)" class="btn_item_wrapper">
                    <span class="btn_item_">Читать</span>
                  </p>
                  }
                  @if(item.youtube) {
                  <p (click)="openLecture(item.external_id)" class="btn_item_wrapper">
                    <span class="btn_item_">Смотреть</span>
                  </p>
                  }
                </div>
                <div class="invis_part">

                  <div class="flex">
                    <div class="flex flex-col icons_w b_p">
                      <div class="tr_descr">{{ item.description }}</div>
                      <span class="mt-u_ text-color">Длительность: {{Math.ceil(item.duration / 60)}} мин.</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            } @else if (item.type == 'content') {
            <div [ngClass]="{'widen': isOpened_[i], 'last': last}" class="article-item m">
              <div class="vis_part relative">
                <div (click)="isOpened_[i] = !isOpened_[i]" class="art_img_"></div>
                <div (click)="isOpened_[i] = !isOpened_[i]" class="art_img">
                  @if(item.preview) {
                  <img style="object-fit: cover" width="66" height="66"
                    [ngSrc]="environment.serverUrl + '/upload/' + item.preview.name">
                  } @else {
                  <img src="assets/images/clouds.webp" alt="image">
                  }
                </div>
                <div class="flex justify-between">
                  <div class="titl_w">
                    <div class="article-title" (click)="$event.stopPropagation();navigate(item)">{{item.title}}
                    </div>
                    <div class="article-category">{{ item.author || item.title}}</div>
                  </div>
                  <div class="actions_w" (click)="showMenu(i, $event)" [class.show_menu]="show_menu[i]">
                    <div class="flex items-center cursor-pointer icons_w show_md">
                      <div class="icon-wrap star_w no_h">
                        <svg width="7" height="22" viewBox="0 0 7 22" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <circle cx="3.27778" cy="3.02778" r="2.27778" stroke="var(--text-color)" />
                          <circle cx="3.27778" cy="10.9999" r="2.27778" stroke="var(--text-color)" />
                          <circle cx="3.27778" cy="18.9721" r="2.27778" stroke="var(--text-color)" />
                        </svg>
                      </div>
                    </div>
                    <div class="md_chg">
                      <div title="Добавить в очередь" class="flex items-center cursor-pointer icons_w fav_hov"
                        [ngClass]="{'in-favourites': item.inFavourites}"
                        (click)="favoritesArticle(item);$event.stopPropagation();">
                        <div class="icon-wrap star_w favr_">
                          <svg class="emty_f" width="25" height="24" viewBox="0 0 25 24" fill="none"
                            xmlns="http://www.w3.org/2000/svg">
                            <path
                              d="M5.96868 23.9618C5.94253 23.9618 5.91638 23.9618 5.89022 23.9618C5.36715 23.9356 4.89638 23.6999 4.53023 23.3333C4.16408 22.9143 3.98101 22.3906 4.03332 21.8145C4.05947 21.5789 4.11178 21.317 4.19024 21.0552C4.71331 19.4055 5.41946 17.2844 6.1256 15.1372C4.42562 13.9065 2.72564 12.6758 1.02565 11.4189C0.816424 11.2618 0.162584 10.7904 0.0318159 9.95249C-0.0727985 9.35022 0.0841231 8.77414 0.450273 8.35517C0.842577 7.88383 1.44411 7.62197 2.15026 7.62197C2.90871 7.62197 3.66717 7.62197 4.42562 7.62197H8.55789C8.68866 7.25537 8.79327 6.88878 8.92404 6.52218C9.08096 6.05084 9.21173 5.60568 9.36865 5.13435C9.7348 4.00837 10.0748 2.8562 10.4671 1.73022C10.5979 1.31125 10.8333 0.918472 11.1209 0.604245C11.4871 0.211462 12.0363 -0.0242083 12.5855 0.00197721C13.1348 0.0281628 13.6578 0.290018 14.024 0.735173C14.2332 1.02321 14.4163 1.33744 14.5471 1.70404C15.044 3.24899 15.5409 4.79393 16.0378 6.33888L16.4563 7.62197H17.6593C19.3332 7.62197 21.007 7.62197 22.6808 7.62197C23.047 7.62197 23.387 7.67434 23.7008 7.77909C24.2762 7.98857 24.7208 8.43372 24.9039 9.00981C25.087 9.58589 25.0085 10.1882 24.6685 10.7119C24.4854 10.9999 24.25 11.2356 23.9624 11.4451C22.2624 12.6758 20.5624 13.9327 18.8886 15.1372C19.5424 17.1273 20.1962 19.0912 20.8239 21.0813C21.0593 21.7884 21.0332 22.3906 20.7455 22.9405C20.327 23.7785 19.3593 24.1974 18.4701 23.9094C18.1301 23.8046 17.8163 23.6475 17.5286 23.438C16.1424 22.443 14.7563 21.4218 13.344 20.4005L12.5071 19.7983L11.5132 20.5314C10.1794 21.5003 8.84558 22.4954 7.51174 23.4642C6.96252 23.7785 6.4656 23.9618 5.96868 23.9618ZM3.45794 9.14073C3.01333 9.14073 2.59487 9.14073 2.15026 9.14073C1.91487 9.14073 1.7318 9.21929 1.65334 9.32403C1.57488 9.40259 1.54872 9.53352 1.57488 9.66445C1.60103 9.76919 1.65334 9.90012 1.94103 10.1096C3.71947 11.3927 5.47176 12.702 7.25021 13.9851C7.69482 14.3255 7.85174 14.7968 7.69482 15.3205C6.96252 17.5463 6.23022 19.7983 5.68099 21.5003C5.62869 21.6312 5.60253 21.7884 5.60253 21.8931C5.60253 22.0502 5.62869 22.155 5.70715 22.2597C5.75945 22.3383 5.86407 22.3644 5.94253 22.3644C6.0733 22.3644 6.23022 22.3383 6.54406 22.1026C7.87789 21.1337 9.21173 20.1387 10.5456 19.1698L12.4809 17.7558L14.2594 19.0389C15.6455 20.0601 17.0317 21.0552 18.4178 22.0764C18.5747 22.1811 18.7316 22.2597 18.9147 22.3121C19.0716 22.3644 19.2286 22.2859 19.307 22.1288C19.3593 22.024 19.4116 21.8407 19.2809 21.4741C18.6009 19.4055 17.9209 17.3368 17.2409 15.2682C17.0578 14.7183 17.2147 14.2731 17.6593 13.9327C19.4116 12.6758 21.1901 11.3665 22.9424 10.0834C23.0731 9.97867 23.1777 9.87393 23.2824 9.743C23.3608 9.63826 23.387 9.50733 23.3347 9.40259C23.3085 9.29785 23.2039 9.21929 23.0993 9.16692C22.9685 9.11455 22.7854 9.08836 22.6024 9.08836C20.9285 9.08836 19.2547 9.08836 17.5809 9.08836H15.2794L15.0963 8.59084C15.0701 8.51228 15.044 8.45991 15.0178 8.40754L14.4948 6.78404C13.9978 5.23909 13.5009 3.69414 13.004 2.14919C12.9517 1.96589 12.8471 1.78259 12.7425 1.65167C12.664 1.54692 12.5332 1.52074 12.4809 1.52074C12.4286 1.52074 12.324 1.52074 12.2455 1.62548C12.1148 1.75641 12.0102 1.96589 11.9317 2.17538C11.5656 3.30136 11.1994 4.45352 10.8333 5.55331C10.6763 6.02465 10.5456 6.46981 10.3886 6.94115C10.2579 7.30775 10.1533 7.70053 10.0225 8.06713L9.68249 9.14073H4.42562C4.11178 9.14073 3.77178 9.14073 3.45794 9.14073Z"
                              fill="var(--font-color1)" />
                          </svg>
                          <svg class="emty_f_hover" width="25" height="24" viewBox="0 0 25 24" fill="none"
                            xmlns="http://www.w3.org/2000/svg">
                            <path
                              d="M5.96868 23.9618C5.94253 23.9618 5.91638 23.9618 5.89022 23.9618C5.36715 23.9356 4.89638 23.6999 4.53023 23.3333C4.16408 22.9143 3.98101 22.3906 4.03332 21.8145C4.05947 21.5789 4.11178 21.317 4.19024 21.0552C4.71331 19.4055 5.41946 17.2844 6.1256 15.1372C4.42562 13.9065 2.72564 12.6758 1.02565 11.4189C0.816424 11.2618 0.162584 10.7904 0.0318159 9.95249C-0.0727985 9.35022 0.0841231 8.77414 0.450273 8.35517C0.842577 7.88383 1.44411 7.62197 2.15026 7.62197C2.90871 7.62197 3.66717 7.62197 4.42562 7.62197H8.55789C8.68866 7.25537 8.79327 6.88878 8.92404 6.52218C9.08096 6.05084 9.21173 5.60568 9.36865 5.13435C9.7348 4.00837 10.0748 2.8562 10.4671 1.73022C10.5979 1.31125 10.8333 0.918472 11.1209 0.604245C11.4871 0.211462 12.0363 -0.0242083 12.5855 0.00197721C13.1348 0.0281628 13.6578 0.290018 14.024 0.735173C14.2332 1.02321 14.4163 1.33744 14.5471 1.70404C15.044 3.24899 15.5409 4.79393 16.0378 6.33888L16.4563 7.62197H17.6593C19.3332 7.62197 21.007 7.62197 22.6808 7.62197C23.047 7.62197 23.387 7.67434 23.7008 7.77909C24.2762 7.98857 24.7208 8.43372 24.9039 9.00981C25.087 9.58589 25.0085 10.1882 24.6685 10.7119C24.4854 10.9999 24.25 11.2356 23.9624 11.4451C22.2624 12.6758 20.5624 13.9327 18.8886 15.1372C19.5424 17.1273 20.1962 19.0912 20.8239 21.0813C21.0593 21.7884 21.0332 22.3906 20.7455 22.9405C20.327 23.7785 19.3593 24.1974 18.4701 23.9094C18.1301 23.8046 17.8163 23.6475 17.5286 23.438C16.1424 22.443 14.7563 21.4218 13.344 20.4005L12.5071 19.7983L11.5132 20.5314C10.1794 21.5003 8.84558 22.4954 7.51174 23.4642C6.96252 23.7785 6.4656 23.9618 5.96868 23.9618ZM3.45794 9.14073C3.01333 9.14073 2.59487 9.14073 2.15026 9.14073C1.91487 9.14073 1.7318 9.21929 1.65334 9.32403C1.57488 9.40259 1.54872 9.53352 1.57488 9.66445C1.60103 9.76919 1.65334 9.90012 1.94103 10.1096C3.71947 11.3927 5.47176 12.702 7.25021 13.9851C7.69482 14.3255 7.85174 14.7968 7.69482 15.3205C6.96252 17.5463 6.23022 19.7983 5.68099 21.5003C5.62869 21.6312 5.60253 21.7884 5.60253 21.8931C5.60253 22.0502 5.62869 22.155 5.70715 22.2597C5.75945 22.3383 5.86407 22.3644 5.94253 22.3644C6.0733 22.3644 6.23022 22.3383 6.54406 22.1026C7.87789 21.1337 9.21173 20.1387 10.5456 19.1698L12.4809 17.7558L14.2594 19.0389C15.6455 20.0601 17.0317 21.0552 18.4178 22.0764C18.5747 22.1811 18.7316 22.2597 18.9147 22.3121C19.0716 22.3644 19.2286 22.2859 19.307 22.1288C19.3593 22.024 19.4116 21.8407 19.2809 21.4741C18.6009 19.4055 17.9209 17.3368 17.2409 15.2682C17.0578 14.7183 17.2147 14.2731 17.6593 13.9327C19.4116 12.6758 21.1901 11.3665 22.9424 10.0834C23.0731 9.97867 23.1777 9.87393 23.2824 9.743C23.3608 9.63826 23.387 9.50733 23.3347 9.40259C23.3085 9.29785 23.2039 9.21929 23.0993 9.16692C22.9685 9.11455 22.7854 9.08836 22.6024 9.08836C20.9285 9.08836 19.2547 9.08836 17.5809 9.08836H15.2794L15.0963 8.59084C15.0701 8.51228 15.044 8.45991 15.0178 8.40754L14.4948 6.78404C13.9978 5.23909 13.5009 3.69414 13.004 2.14919C12.9517 1.96589 12.8471 1.78259 12.7425 1.65167C12.664 1.54692 12.5332 1.52074 12.4809 1.52074C12.4286 1.52074 12.324 1.52074 12.2455 1.62548C12.1148 1.75641 12.0102 1.96589 11.9317 2.17538C11.5656 3.30136 11.1994 4.45352 10.8333 5.55331C10.6763 6.02465 10.5456 6.46981 10.3886 6.94115C10.2579 7.30775 10.1533 7.70053 10.0225 8.06713L9.68249 9.14073H4.42562C4.11178 9.14073 3.77178 9.14073 3.45794 9.14073Z"
                              fill="var(--text-color)" />
                          </svg>
                        </div>
                        <p class="show_p_md cdf">
                          добавить в избранное
                        </p>
                        <div class="on_hov">
                          добавить в избранное
                        </div>
                      </div>
                      <div class="flex items-center cursor-pointer icons_w shr_hov"
                        (click)="shareArticle(item);$event.stopPropagation();">
                        <div class="icon-wrap share_w">
                          <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                            xmlns="http://www.w3.org/2000/svg">
                            <path
                              d="M15.9238 7.01009C13.5066 8.21941 11.0965 9.42157 8.66504 10.638C8.9368 11.5325 8.94395 12.427 8.66504 13.3357C11.0822 14.5451 13.4923 15.7544 15.8737 16.9422C16.2456 16.5988 16.5745 16.2267 16.9607 15.9476C19.6353 13.9941 23.4542 15.6113 23.9334 18.8957C24.3481 21.7509 22.0168 24.2268 19.1347 23.9835C17.0894 23.8117 15.3731 22.1516 15.1514 20.1051C15.1085 19.6829 15.1156 19.2464 15.1729 18.8313C15.2086 18.588 15.1442 18.5093 14.9512 18.4163C12.8772 17.3859 10.8105 16.3483 8.73656 15.3107C8.50056 15.1891 8.25741 15.0817 8.02141 14.9458C7.87838 14.8671 7.79972 14.8885 7.6853 15.0102C6.76276 15.9834 5.63283 16.4556 4.28836 16.4413C2.21445 16.4127 0.326467 14.7454 0.0475605 12.6846C-0.288557 10.2588 1.18464 8.12638 3.58752 7.63979C5.16084 7.32494 6.52676 7.79722 7.65669 8.94213C7.80687 9.09241 7.89984 9.10672 8.07862 9.01369C10.3742 7.85447 12.677 6.70955 14.9798 5.56464C15.1585 5.47877 15.2158 5.40005 15.1729 5.18538C14.7295 2.75959 16.4458 0.39105 18.8773 0.047575C21.4232 -0.317367 23.7117 1.45725 23.9763 3.99753C24.2266 6.37323 22.4673 8.57004 20.093 8.8348C18.4196 9.028 17.0751 8.41977 16.0239 7.12458C15.9881 7.09596 15.9595 7.06018 15.9238 7.01009ZM7.14179 11.9976C7.14179 10.5021 5.91174 9.26414 4.42424 9.2713C2.94389 9.27845 1.721 10.4949 1.70669 11.9762C1.69239 13.4574 2.92244 14.7025 4.41709 14.7168C5.90459 14.7311 7.14179 13.5003 7.14179 11.9976ZM16.832 19.5541C16.8248 21.0496 18.0549 22.2876 19.5424 22.2804C21.0227 22.2804 22.2671 21.0353 22.2671 19.5541C22.2599 18.0728 21.0513 16.8635 19.5567 16.8492C18.062 16.8277 16.8391 18.0442 16.832 19.5541ZM19.5424 7.14605C21.037 7.14605 22.2671 5.91527 22.2671 4.42688C22.2599 2.93849 21.0156 1.70055 19.5352 1.7077C18.0477 1.71486 16.8463 2.93133 16.832 4.41972C16.8248 5.92242 18.0406 7.14605 19.5424 7.14605Z"
                              fill="var(--font-color1)" />
                          </svg>
                        </div>
                        <p class="show_p_md">
                          поделиться
                        </p>
                        <div class="on_hov">
                          поделиться
                        </div>
                      </div>
                      <button class="flex items-center icons_w lik_hov" [ngClass]="{'is-liked': item.liked}"
                        (click)="likeArticle(item);$event.stopPropagation();">
                        <div class="icon-wrap like_w">
                          <svg class="emty_l" width="24" height="22" viewBox="0 0 24 22" fill="none"
                            xmlns="http://www.w3.org/2000/svg">
                            <path
                              d="M11.7534 2.35249C11.576 2.19912 11.4123 2.05272 11.2486 1.89935C10.048 0.749078 8.90875 0.163484 7.67404 0.0589139C7.44892 0.0379998 7.22381 0.0310285 7.00552 0.0310285C4.87718 0.0310285 3.11038 0.909419 1.72559 2.65923C0.286236 4.47875 -0.252671 6.65381 0.108874 9.12864C0.402203 11.1294 1.2549 12.7816 2.63969 14.0365C4.74757 15.9397 6.91684 17.8428 9.01789 19.6763C9.79555 20.3595 10.5732 21.0357 11.3509 21.7189C11.4668 21.8235 11.5896 21.9002 11.7056 21.942C12.0399 22.0675 12.3468 21.9908 12.6674 21.698C13.8612 20.6453 15.055 19.5926 16.2556 18.54C17.0742 17.815 17.8996 17.0969 18.7182 16.3719C19.0047 16.1209 19.2981 15.8769 19.5777 15.6329C20.2258 15.0822 20.8943 14.5175 21.4946 13.8971C23.6229 11.6802 24.4006 9.03105 23.8071 6.03336C23.3023 3.49579 21.9517 1.69718 19.796 0.679364C17.7291 -0.296625 15.7713 -0.21994 13.9908 0.902448C13.3223 1.32073 12.7016 1.87844 12.1422 2.55466L12.074 2.63831L11.9989 2.5686C11.9171 2.49192 11.8352 2.4222 11.7534 2.35249ZM12.7561 4.47875C12.8175 4.38812 12.8789 4.2975 12.9403 4.20687C13.1586 3.87922 13.3837 3.54459 13.6566 3.25877C15.13 1.69021 17.4426 1.39044 19.4072 2.52677C20.8602 3.36334 21.7538 4.69486 22.0744 6.47953C22.5315 9.0659 21.7675 11.2689 19.7892 13.0187C18.0224 14.5802 16.2215 16.1697 14.4752 17.6964C13.7316 18.3517 12.9881 19.0001 12.2513 19.6554C12.2172 19.6902 12.1763 19.7181 12.1354 19.753L11.9989 19.8645L8.01512 16.3579C7.59218 15.9815 7.15559 15.605 6.73265 15.2425C5.79809 14.4338 4.82943 13.5973 3.90851 12.7328C2.5783 11.485 1.8211 9.77698 1.78699 7.92957C1.75288 6.17976 2.37365 4.51361 3.49921 3.36334C5.02043 1.80872 7.43528 1.39741 9.23618 2.38037C9.94563 2.77077 10.5459 3.35636 11.0576 4.17201C11.0848 4.21384 11.1121 4.25567 11.1326 4.2975C11.1667 4.36024 11.2008 4.41601 11.2417 4.47178C11.4191 4.73669 11.692 4.89703 11.9853 4.89703H11.9921C12.2923 4.904 12.5719 4.75063 12.7561 4.47875Z"
                              fill="var(--font-color1)" />
                          </svg>
                          <svg class="emty_l_hover" width="24" height="22" viewBox="0 0 24 22" fill="none"
                            xmlns="http://www.w3.org/2000/svg">
                            <path
                              d="M11.7534 2.35249C11.576 2.19912 11.4123 2.05272 11.2486 1.89935C10.048 0.749078 8.90875 0.163484 7.67404 0.0589139C7.44892 0.0379998 7.22381 0.0310285 7.00552 0.0310285C4.87718 0.0310285 3.11038 0.909419 1.72559 2.65923C0.286236 4.47875 -0.252671 6.65381 0.108874 9.12864C0.402203 11.1294 1.2549 12.7816 2.63969 14.0365C4.74757 15.9397 6.91684 17.8428 9.01789 19.6763C9.79555 20.3595 10.5732 21.0357 11.3509 21.7189C11.4668 21.8235 11.5896 21.9002 11.7056 21.942C12.0399 22.0675 12.3468 21.9908 12.6674 21.698C13.8612 20.6453 15.055 19.5926 16.2556 18.54C17.0742 17.815 17.8996 17.0969 18.7182 16.3719C19.0047 16.1209 19.2981 15.8769 19.5777 15.6329C20.2258 15.0822 20.8943 14.5175 21.4946 13.8971C23.6229 11.6802 24.4006 9.03105 23.8071 6.03336C23.3023 3.49579 21.9517 1.69718 19.796 0.679364C17.7291 -0.296625 15.7713 -0.21994 13.9908 0.902448C13.3223 1.32073 12.7016 1.87844 12.1422 2.55466L12.074 2.63831L11.9989 2.5686C11.9171 2.49192 11.8352 2.4222 11.7534 2.35249ZM12.7561 4.47875C12.8175 4.38812 12.8789 4.2975 12.9403 4.20687C13.1586 3.87922 13.3837 3.54459 13.6566 3.25877C15.13 1.69021 17.4426 1.39044 19.4072 2.52677C20.8602 3.36334 21.7538 4.69486 22.0744 6.47953C22.5315 9.0659 21.7675 11.2689 19.7892 13.0187C18.0224 14.5802 16.2215 16.1697 14.4752 17.6964C13.7316 18.3517 12.9881 19.0001 12.2513 19.6554C12.2172 19.6902 12.1763 19.7181 12.1354 19.753L11.9989 19.8645L8.01512 16.3579C7.59218 15.9815 7.15559 15.605 6.73265 15.2425C5.79809 14.4338 4.82943 13.5973 3.90851 12.7328C2.5783 11.485 1.8211 9.77698 1.78699 7.92957C1.75288 6.17976 2.37365 4.51361 3.49921 3.36334C5.02043 1.80872 7.43528 1.39741 9.23618 2.38037C9.94563 2.77077 10.5459 3.35636 11.0576 4.17201C11.0848 4.21384 11.1121 4.25567 11.1326 4.2975C11.1667 4.36024 11.2008 4.41601 11.2417 4.47178C11.4191 4.73669 11.692 4.89703 11.9853 4.89703H11.9921C12.2923 4.904 12.5719 4.75063 12.7561 4.47875Z"
                              fill="var(--text-color)" />
                          </svg>
                        </div>
                        <p class="show_p_md">
                          мне нравится
                        </p>
                        <span class="ml-2 text-color default_">
                          {{item.likes}}
                        </span>

                        <div class="on_hov">
                          мне нравится
                        </div>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
              <div class="invis_part">
                <div class="article-content" [innerHTML]="item.content"></div>
                <p (click)="$event.stopPropagation();navigate(item)" class="link_more">
                  Читать далее
                </p>
                <div class="tags_cont">
                  @for (tag of item.tags; track $index) {
                  <!-- (click)="$event.preventDefault(); addTagFilter(tag)" -->
                  <a style="cursor: pointer;">
                    <div class="tag_item">{{tag.name}}</div>
                  </a>
                  }
                </div>
                <div class="flex">
                  <div class="flex items-center icons_w b_p">
                    <div class="icon-wrap clock_w">
                      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                          d="M11.25 0C11.75 0 12.25 0 12.75 0C12.8125 0.0125261 12.8687 0.0313152 12.9312 0.0375783C13.6812 0.169102 14.4625 0.212944 15.1812 0.438413C19.9188 1.8977 22.8062 5.06681 23.8125 9.93319C23.9 10.3653 23.9375 10.81 24 11.2484C24 11.7495 24 12.2505 24 12.7516C23.9875 12.8142 23.9688 12.8706 23.9625 12.9332C23.8375 14.5804 23.4187 16.1524 22.6125 17.5992C20.3438 21.6451 16.8875 23.8121 12.25 23.9937C9.75625 24.0939 7.44375 23.3987 5.36875 22.0021C2.54375 20.0856 0.8125 17.4489 0.1875 14.0919C0.10625 13.6472 0.0625 13.1962 0 12.7516C0 12.2505 0 11.7495 0 11.2484C0.0125 11.1795 0.03125 11.1106 0.0375 11.0418C0.11875 10.4969 0.16875 9.93946 0.28125 9.40084C1.25 4.74739 5.21875 0.951983 9.90625 0.187891C10.3562 0.112735 10.8 0.0626305 11.25 0ZM1.925 12.9395C2.375 18.0125 6.55 21.7328 11.0625 22.0647C11.0625 21.8017 11.0625 21.5386 11.0625 21.2693C11.0688 20.6931 11.4563 20.286 12 20.286C12.5437 20.286 12.9312 20.6931 12.9375 21.2693C12.9375 21.5324 12.9375 21.7954 12.9375 22.0647C17.7875 21.714 21.75 17.5741 22.0562 12.9395C21.9375 12.9395 21.825 12.9395 21.7062 12.9395C20.7125 12.9395 20.2812 12.6514 20.2812 11.9937C20.2875 11.3486 20.7125 11.0605 21.6938 11.0605C21.8188 11.0605 21.9375 11.0605 22.0625 11.0605C21.65 5.92484 17.2875 2.19207 12.9375 1.94781C12.9375 2.20459 12.9375 2.46138 12.9375 2.71816C12.9312 3.30689 12.5437 3.72025 11.9875 3.71399C11.45 3.70772 11.0625 3.30063 11.0625 2.71816C11.0625 2.45511 11.0625 2.19207 11.0625 1.92902C5.75 2.39248 2.15625 6.90188 1.95 11.0668C2.19375 11.0668 2.43125 11.0668 2.675 11.0668C3.29375 11.0668 3.70625 11.4426 3.7125 12C3.7125 12.5637 3.3 12.9395 2.6625 12.9457C2.425 12.9395 2.18125 12.9395 1.925 12.9395Z"
                          fill="var(--text-color)" />
                        <path
                          d="M11.0627 9.23796C11.0627 8.34234 11.0627 7.44047 11.0627 6.54485C11.0627 5.95612 11.4565 5.5365 12.0002 5.5365C12.544 5.5365 12.9377 5.95612 12.9377 6.55111C12.9377 8.15445 12.944 9.75153 12.9315 11.3549C12.9315 11.5428 12.9815 11.6743 13.1127 11.8058C14.1752 12.858 15.2252 13.9165 16.2815 14.9686C16.5065 15.1941 16.6502 15.4572 16.619 15.7828C16.5815 16.1649 16.3815 16.4405 16.019 16.5783C15.6377 16.7223 15.294 16.6346 15.0065 16.3465C14.3377 15.6889 13.6815 15.025 13.019 14.3611C12.494 13.835 11.9752 13.3089 11.444 12.7891C11.1815 12.5323 11.0565 12.2442 11.0627 11.8747C11.069 10.9854 11.0627 10.1085 11.0627 9.23796Z"
                          fill="var(--text-color)" />
                      </svg>
                    </div>
                    <span class="ml-2 text-color">{{item.content | readingTime}}</span>
                  </div>
                  <div class="flex items-center icons_w b_p">
                    <div class="icon-wrap cal_w">
                      <svg width="22" height="24" viewBox="0 0 22 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                          d="M6.41826 5.02326C5.96059 5.02326 5.58105 4.64372 5.58105 4.18605V0.837209C5.58105 0.379535 5.96059 0 6.41826 0C6.87594 0 7.25547 0.379535 7.25547 0.837209V4.18605C7.25547 4.64372 6.87594 5.02326 6.41826 5.02326Z"
                          fill="var(--text-color)" />
                        <path
                          d="M15.3489 5.02326C14.8913 5.02326 14.5117 4.64372 14.5117 4.18605V0.837209C14.5117 0.379535 14.8913 0 15.3489 0C15.8066 0 16.1861 0.379535 16.1861 0.837209V4.18605C16.1861 4.64372 15.8066 5.02326 15.3489 5.02326Z"
                          fill="var(--text-color)" />
                        <path
                          d="M6.97663 14.7908C6.83151 14.7908 6.6864 14.7573 6.55244 14.7015C6.40733 14.6457 6.2957 14.5676 6.18407 14.4671C5.98314 14.255 5.86035 13.9759 5.86035 13.6745C5.86035 13.5294 5.89384 13.3843 5.94965 13.2503C6.00547 13.1164 6.08361 12.9936 6.18407 12.882C6.2957 12.7815 6.40733 12.7034 6.55244 12.6476C6.9543 12.4801 7.45663 12.5694 7.76919 12.882C7.97012 13.0941 8.09291 13.3843 8.09291 13.6745C8.09291 13.7415 8.08175 13.8196 8.07058 13.8978C8.05942 13.9648 8.0371 14.0317 8.00361 14.0987C7.98128 14.1657 7.94779 14.2327 7.90314 14.2996C7.86965 14.3555 7.81384 14.4113 7.76919 14.4671C7.5571 14.668 7.26686 14.7908 6.97663 14.7908Z"
                          fill="var(--text-color)" />
                        <path
                          d="M10.8839 14.7906C10.7387 14.7906 10.5936 14.7572 10.4597 14.7013C10.3146 14.6455 10.2029 14.5674 10.0913 14.4669C9.89037 14.2548 9.76758 13.9758 9.76758 13.6744C9.76758 13.5292 9.80107 13.3841 9.85688 13.2502C9.91269 13.1162 9.99083 12.9934 10.0913 12.8818C10.2029 12.7813 10.3146 12.7032 10.4597 12.6474C10.8615 12.4688 11.3639 12.5692 11.6764 12.8818C11.8773 13.0939 12.0001 13.3841 12.0001 13.6744C12.0001 13.7413 11.989 13.8195 11.9778 13.8976C11.9666 13.9646 11.9443 14.0316 11.9108 14.0986C11.8885 14.1655 11.855 14.2325 11.8104 14.2995C11.7769 14.3553 11.7211 14.4111 11.6764 14.4669C11.4643 14.6679 11.1741 14.7906 10.8839 14.7906Z"
                          fill="var(--text-color)" />
                        <path
                          d="M14.7911 14.7906C14.646 14.7906 14.5009 14.7572 14.3669 14.7013C14.2218 14.6455 14.1102 14.5674 13.9985 14.4669C13.9539 14.4111 13.9092 14.3553 13.8646 14.2995C13.8199 14.2325 13.7864 14.1655 13.7641 14.0986C13.7306 14.0316 13.7083 13.9646 13.6971 13.8976C13.686 13.8195 13.6748 13.7413 13.6748 13.6744C13.6748 13.3841 13.7976 13.0939 13.9985 12.8818C14.1102 12.7813 14.2218 12.7032 14.3669 12.6474C14.7799 12.4688 15.2711 12.5692 15.5836 12.8818C15.7846 13.0939 15.9074 13.3841 15.9074 13.6744C15.9074 13.7413 15.8962 13.8195 15.885 13.8976C15.8739 13.9646 15.8515 14.0316 15.8181 14.0986C15.7957 14.1655 15.7622 14.2325 15.7176 14.2995C15.6841 14.3553 15.6283 14.4111 15.5836 14.4669C15.3715 14.6679 15.0813 14.7906 14.7911 14.7906Z"
                          fill="var(--text-color)" />
                        <path
                          d="M6.97663 18.6976C6.83151 18.6976 6.6864 18.6642 6.55244 18.6084C6.41849 18.5526 6.2957 18.4744 6.18407 18.3739C5.98314 18.1618 5.86035 17.8716 5.86035 17.5813C5.86035 17.4362 5.89384 17.2911 5.94965 17.1572C6.00547 17.012 6.08361 16.8894 6.18407 16.7889C6.5971 16.3759 7.35617 16.3759 7.76919 16.7889C7.97012 17.001 8.09291 17.2911 8.09291 17.5813C8.09291 17.8716 7.97012 18.1618 7.76919 18.3739C7.5571 18.5748 7.26686 18.6976 6.97663 18.6976Z"
                          fill="var(--text-color)" />
                        <path
                          d="M10.8839 18.6976C10.5936 18.6976 10.3034 18.5748 10.0913 18.3739C9.89037 18.1618 9.76758 17.8716 9.76758 17.5813C9.76758 17.4362 9.80107 17.2911 9.85688 17.1572C9.91269 17.012 9.99083 16.8894 10.0913 16.7889C10.5043 16.3759 11.2634 16.3759 11.6764 16.7889C11.7769 16.8894 11.855 17.012 11.9108 17.1572C11.9666 17.2911 12.0001 17.4362 12.0001 17.5813C12.0001 17.8716 11.8773 18.1618 11.6764 18.3739C11.4643 18.5748 11.1741 18.6976 10.8839 18.6976Z"
                          fill="var(--text-color)" />
                        <path
                          d="M14.7911 18.6975C14.5009 18.6975 14.2106 18.5747 13.9985 18.3738C13.8981 18.2733 13.8199 18.1506 13.7641 18.0054C13.7083 17.8715 13.6748 17.7264 13.6748 17.5813C13.6748 17.4361 13.7083 17.291 13.7641 17.1571C13.8199 17.0119 13.8981 16.8892 13.9985 16.7887C14.2553 16.5319 14.646 16.4092 15.0032 16.4873C15.0813 16.4985 15.1483 16.5208 15.2153 16.5543C15.2822 16.5766 15.3492 16.6101 15.4162 16.6547C15.472 16.6882 15.5278 16.744 15.5836 16.7887C15.7846 17.0008 15.9074 17.291 15.9074 17.5813C15.9074 17.8715 15.7846 18.1617 15.5836 18.3738C15.3715 18.5747 15.0813 18.6975 14.7911 18.6975Z"
                          fill="var(--text-color)" />
                        <path
                          d="M20.3716 9.5886H1.39483C0.937152 9.5886 0.557617 9.20907 0.557617 8.75139C0.557617 8.29372 0.937152 7.91418 1.39483 7.91418H20.3716C20.8292 7.91418 21.2088 8.29372 21.2088 8.75139C21.2088 9.20907 20.8292 9.5886 20.3716 9.5886Z"
                          fill="var(--text-color)" />
                        <path
                          d="M15.3488 24H6.4186C2.34419 24 0 21.6558 0 17.5814V8.09304C0 4.01862 2.34419 1.67444 6.4186 1.67444H15.3488C19.4233 1.67444 21.7674 4.01862 21.7674 8.09304V17.5814C21.7674 21.6558 19.4233 24 15.3488 24ZM6.4186 3.34886C3.22605 3.34886 1.67442 4.90049 1.67442 8.09304V17.5814C1.67442 20.774 3.22605 22.3256 6.4186 22.3256H15.3488C18.5414 22.3256 20.093 20.774 20.093 17.5814V8.09304C20.093 4.90049 18.5414 3.34886 15.3488 3.34886H6.4186Z"
                          fill="var(--text-color)" />
                      </svg>
                    </div>
                    <span class="ml-2 text-color">{{item.created_at | date: 'dd/MM/yyyy'}}</span>
                  </div>
                </div>
              </div>
            </div>
            }
            @else if (item.type == 'library') {
            <div>
              <div [ngClass]="{'widen': isOpened[i], 'last': last}" class="article-item relative">
                <div class="vis_part">
                  <div (click)="isOpened[i] = !isOpened[i]" class="art_img_"></div>
                  <div class="art_img">
                    <img [src]="item.image" alt="img">
                  </div>
                  <div (click)="isOpened[i] = !isOpened[i]" class="flex justify-between">
                    <div class="titl_w">
                      <a class="article-title" [routerLink]="['/ru/library/' + item.code]">
                        {{item.title}}
                      </a>
                      <div class="article-category">
                        Автор: {{item.author}} Чтец: {{item.reader}}</div>
                      <div class="flex items-center icons_w b_p">
                        <div class=" flex items-center">
                          <img class="mr-2" src="assets/images/icons/chck.svg" alt="">
                          <span class="ml-1 text-color">
                            {{item.views}} просмотров
                          </span>
                        </div>
                      </div>
                    </div>
                    <div class="actions_w" (click)="showMenu(i, $event)" [class.show_menu]="show_menu[i]">
                      <button class="flex items-center icons_w lik_hov show_md separatre_"
                        (click)="null;$event.stopPropagation()">
                        <div class="icon-wrap like_w no_h">
                          <svg class="emty_l" width="21" height="22" viewBox="0 0 21 22" fill="none"
                            xmlns="http://www.w3.org/2000/svg">
                            <path
                              d="M19.4948 12.1068C17.2112 10.6027 14.8396 10.3315 12.4366 11.589C10.0398 12.8404 8.93264 14.9055 8.90748 17.5685C8.81941 17.5685 8.74392 17.5685 8.66843 17.5685C7.42916 17.5685 6.18989 17.5685 4.95061 17.5685C4.36558 17.5685 4.12024 17.3342 4.12024 16.7548C4.12024 11.4411 4.12024 6.12739 4.12024 0.813697C4.11395 0.234246 4.35299 0 4.94432 0C8.97039 0 12.9964 0 17.0225 0C18.4945 0 19.4948 0.9863 19.4948 2.4226C19.4948 5.57259 19.4948 8.72875 19.4948 11.8787C19.4948 11.9466 19.4948 12.0144 19.4948 12.1068ZM11.8201 6.20136C12.9021 6.20136 13.9841 6.20136 15.0661 6.20136C15.5316 6.20136 15.8399 5.94862 15.8461 5.5541C15.8524 5.16574 15.5442 4.87602 15.0913 4.87602C12.9021 4.86985 10.7129 4.87602 8.52374 4.87602C8.39164 4.87602 8.25324 4.90684 8.14001 4.95616C7.85693 5.08561 7.71224 5.39999 7.78773 5.68972C7.86322 5.99177 8.12114 6.20136 8.45455 6.20136C9.5743 6.20136 10.694 6.20136 11.8201 6.20136ZM11.8201 8.96916C12.3359 8.96916 12.8518 8.96916 13.3739 8.96916C13.808 8.96916 14.1225 8.69177 14.1225 8.30341C14.1225 7.92122 13.8142 7.63766 13.3739 7.63766C12.3296 7.63149 11.2917 7.63149 10.2474 7.63766C9.80076 7.63766 9.51139 7.91506 9.51139 8.30341C9.51139 8.69177 9.81334 8.963 10.2537 8.963C10.7695 8.97533 11.2917 8.96916 11.8201 8.96916Z"
                              fill="var(--font-color1)" />
                            <path
                              d="M3.04471 0.0124731C3.02584 0.406993 2.98809 0.789184 2.98809 1.17138C2.9818 6.27548 2.9818 11.3857 2.98809 16.4898C2.98809 16.8412 3.03213 17.1926 3.051 17.5563C2.99438 17.5624 2.89373 17.5748 2.79308 17.5809C1.90609 17.6549 1.39025 18.407 1.36509 19.1837C1.33992 19.9172 1.98787 20.5707 2.7805 20.657C2.90631 20.6693 3.03213 20.6755 3.15794 20.6755C4.98854 20.6755 6.81914 20.6755 8.64345 20.6755C8.73782 20.6755 8.83218 20.6755 8.87621 20.6755C9.05235 21.1316 9.21591 21.557 9.37947 21.9885C9.33543 21.9885 9.26624 21.9946 9.19704 21.9946C7.13368 21.9946 5.07032 22.0008 3.00068 21.9946C1.51606 21.9885 0.333408 21.0576 0.0566165 19.6645C-0.00629073 19.3563 0.00629072 19.0357 0.00629072 18.7213C0.00629072 13.4816 0.00629072 8.24808 0 3.00836C0 1.86179 0.478095 0.974115 1.48461 0.376171C1.98158 0.0987743 2.51629 -0.0306776 3.04471 0.0124731Z"
                              fill="var(--font-color1)" />
                            <path
                              d="M19.6212 17.1801C19.6149 14.9363 17.5956 13.1178 15.2303 13.2719C12.6826 13.4383 11.3678 15.6452 11.4433 17.1739C11.8711 17.1739 12.3051 17.1739 12.7329 17.1739C13.7205 17.1801 14.4062 17.8397 14.4251 18.8013C14.4314 19.3253 14.4377 19.8493 14.4251 20.3733C14.4062 21.3164 13.7331 21.976 12.7706 21.9945C12.3554 22.0007 11.9403 22.0068 11.5251 21.9822C10.8016 21.939 10.1034 21.3287 10.0908 20.626C10.0593 19.2513 9.99642 17.8644 10.1411 16.502C10.399 14.1041 12.4813 12.2055 14.9158 11.9589C17.7843 11.6753 20.3132 13.4877 20.8857 16.2493C20.9611 16.613 20.9863 16.9952 20.9926 17.3712C21.0052 18.3328 20.9989 19.2945 20.9989 20.2561C20.9989 21.3226 20.3195 21.9883 19.2375 21.9945C18.8852 21.9945 18.5329 22.0007 18.1806 21.9883C17.3125 21.9513 16.6457 21.2794 16.6268 20.4349C16.6143 19.8678 16.6143 19.2945 16.6268 18.7274C16.6457 17.8644 17.344 17.1863 18.2247 17.1739C18.6776 17.1678 19.1431 17.1801 19.6212 17.1801Z"
                              fill="var(--font-color1)" />
                          </svg>
                          <svg class="emty_l_hover" width="21" height="22" viewBox="0 0 21 22" fill="none"
                            xmlns="http://www.w3.org/2000/svg">
                            <path
                              d="M19.4948 12.1068C17.2112 10.6027 14.8396 10.3315 12.4366 11.589C10.0398 12.8404 8.93264 14.9055 8.90748 17.5685C8.81941 17.5685 8.74392 17.5685 8.66843 17.5685C7.42916 17.5685 6.18989 17.5685 4.95061 17.5685C4.36558 17.5685 4.12024 17.3342 4.12024 16.7548C4.12024 11.4411 4.12024 6.12739 4.12024 0.813697C4.11395 0.234246 4.35299 0 4.94432 0C8.97039 0 12.9964 0 17.0225 0C18.4945 0 19.4948 0.9863 19.4948 2.4226C19.4948 5.57259 19.4948 8.72875 19.4948 11.8787C19.4948 11.9466 19.4948 12.0144 19.4948 12.1068ZM11.8201 6.20136C12.9021 6.20136 13.9841 6.20136 15.0661 6.20136C15.5316 6.20136 15.8399 5.94862 15.8461 5.5541C15.8524 5.16574 15.5442 4.87602 15.0913 4.87602C12.9021 4.86985 10.7129 4.87602 8.52374 4.87602C8.39164 4.87602 8.25324 4.90684 8.14001 4.95616C7.85693 5.08561 7.71224 5.39999 7.78773 5.68972C7.86322 5.99177 8.12114 6.20136 8.45455 6.20136C9.5743 6.20136 10.694 6.20136 11.8201 6.20136ZM11.8201 8.96916C12.3359 8.96916 12.8518 8.96916 13.3739 8.96916C13.808 8.96916 14.1225 8.69177 14.1225 8.30341C14.1225 7.92122 13.8142 7.63766 13.3739 7.63766C12.3296 7.63149 11.2917 7.63149 10.2474 7.63766C9.80076 7.63766 9.51139 7.91506 9.51139 8.30341C9.51139 8.69177 9.81334 8.963 10.2537 8.963C10.7695 8.97533 11.2917 8.96916 11.8201 8.96916Z"
                              fill="var(--text-color)" />
                            <path
                              d="M3.04471 0.0124731C3.02584 0.406993 2.98809 0.789184 2.98809 1.17138C2.9818 6.27548 2.9818 11.3857 2.98809 16.4898C2.98809 16.8412 3.03213 17.1926 3.051 17.5563C2.99438 17.5624 2.89373 17.5748 2.79308 17.5809C1.90609 17.6549 1.39025 18.407 1.36509 19.1837C1.33992 19.9172 1.98787 20.5707 2.7805 20.657C2.90631 20.6693 3.03213 20.6755 3.15794 20.6755C4.98854 20.6755 6.81914 20.6755 8.64345 20.6755C8.73782 20.6755 8.83218 20.6755 8.87621 20.6755C9.05235 21.1316 9.21591 21.557 9.37947 21.9885C9.33543 21.9885 9.26624 21.9946 9.19704 21.9946C7.13368 21.9946 5.07032 22.0008 3.00068 21.9946C1.51606 21.9885 0.333408 21.0576 0.0566165 19.6645C-0.00629073 19.3563 0.00629072 19.0357 0.00629072 18.7213C0.00629072 13.4816 0.00629072 8.24808 0 3.00836C0 1.86179 0.478095 0.974115 1.48461 0.376171C1.98158 0.0987743 2.51629 -0.0306776 3.04471 0.0124731Z"
                              fill="var(--text-color)" />
                            <path
                              d="M19.6212 17.1801C19.6149 14.9363 17.5956 13.1178 15.2303 13.2719C12.6826 13.4383 11.3678 15.6452 11.4433 17.1739C11.8711 17.1739 12.3051 17.1739 12.7329 17.1739C13.7205 17.1801 14.4062 17.8397 14.4251 18.8013C14.4314 19.3253 14.4377 19.8493 14.4251 20.3733C14.4062 21.3164 13.7331 21.976 12.7706 21.9945C12.3554 22.0007 11.9403 22.0068 11.5251 21.9822C10.8016 21.939 10.1034 21.3287 10.0908 20.626C10.0593 19.2513 9.99642 17.8644 10.1411 16.502C10.399 14.1041 12.4813 12.2055 14.9158 11.9589C17.7843 11.6753 20.3132 13.4877 20.8857 16.2493C20.9611 16.613 20.9863 16.9952 20.9926 17.3712C21.0052 18.3328 20.9989 19.2945 20.9989 20.2561C20.9989 21.3226 20.3195 21.9883 19.2375 21.9945C18.8852 21.9945 18.5329 22.0007 18.1806 21.9883C17.3125 21.9513 16.6457 21.2794 16.6268 20.4349C16.6143 19.8678 16.6143 19.2945 16.6268 18.7274C16.6457 17.8644 17.344 17.1863 18.2247 17.1739C18.6776 17.1678 19.1431 17.1801 19.6212 17.1801Z"
                              fill="var(--text-color)" />
                          </svg>
                        </div>
                        <p class="show_p_md">
                          слушать
                        </p>
                        <div class="on_hov shrt">
                          слушать
                        </div>
                      </button>
                      <div class="flex items-center cursor-pointer icons_w show_md">
                        <div class="icon-wrap star_w no_h">
                          <svg width="7" height="22" viewBox="0 0 7 22" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <circle cx="3.27778" cy="3.27778" r="2.27778" fill="#DEA53D" stroke="#DEA53D" />
                            <circle cx="3.27778" cy="11.2499" r="2.27778" fill="#DEA53D" stroke="#DEA53D" />
                            <circle cx="3.27778" cy="19.2221" r="2.27778" fill="#DEA53D" stroke="#DEA53D" />
                          </svg>
                        </div>
                      </div>
                      <div class="md_chg">
                        <button class="flex items-center icons_w lik_hov d__none" (click)="null">
                          <div class="icon-wrap like_w">
                            <svg class="emty_l" width="21" height="22" viewBox="0 0 21 22" fill="none"
                              xmlns="http://www.w3.org/2000/svg">
                              <path
                                d="M19.4948 12.1068C17.2112 10.6027 14.8396 10.3315 12.4366 11.589C10.0398 12.8404 8.93264 14.9055 8.90748 17.5685C8.81941 17.5685 8.74392 17.5685 8.66843 17.5685C7.42916 17.5685 6.18989 17.5685 4.95061 17.5685C4.36558 17.5685 4.12024 17.3342 4.12024 16.7548C4.12024 11.4411 4.12024 6.12739 4.12024 0.813697C4.11395 0.234246 4.35299 0 4.94432 0C8.97039 0 12.9964 0 17.0225 0C18.4945 0 19.4948 0.9863 19.4948 2.4226C19.4948 5.57259 19.4948 8.72875 19.4948 11.8787C19.4948 11.9466 19.4948 12.0144 19.4948 12.1068ZM11.8201 6.20136C12.9021 6.20136 13.9841 6.20136 15.0661 6.20136C15.5316 6.20136 15.8399 5.94862 15.8461 5.5541C15.8524 5.16574 15.5442 4.87602 15.0913 4.87602C12.9021 4.86985 10.7129 4.87602 8.52374 4.87602C8.39164 4.87602 8.25324 4.90684 8.14001 4.95616C7.85693 5.08561 7.71224 5.39999 7.78773 5.68972C7.86322 5.99177 8.12114 6.20136 8.45455 6.20136C9.5743 6.20136 10.694 6.20136 11.8201 6.20136ZM11.8201 8.96916C12.3359 8.96916 12.8518 8.96916 13.3739 8.96916C13.808 8.96916 14.1225 8.69177 14.1225 8.30341C14.1225 7.92122 13.8142 7.63766 13.3739 7.63766C12.3296 7.63149 11.2917 7.63149 10.2474 7.63766C9.80076 7.63766 9.51139 7.91506 9.51139 8.30341C9.51139 8.69177 9.81334 8.963 10.2537 8.963C10.7695 8.97533 11.2917 8.96916 11.8201 8.96916Z"
                                fill="var(--font-color1)" />
                              <path
                                d="M3.04471 0.0124731C3.02584 0.406993 2.98809 0.789184 2.98809 1.17138C2.9818 6.27548 2.9818 11.3857 2.98809 16.4898C2.98809 16.8412 3.03213 17.1926 3.051 17.5563C2.99438 17.5624 2.89373 17.5748 2.79308 17.5809C1.90609 17.6549 1.39025 18.407 1.36509 19.1837C1.33992 19.9172 1.98787 20.5707 2.7805 20.657C2.90631 20.6693 3.03213 20.6755 3.15794 20.6755C4.98854 20.6755 6.81914 20.6755 8.64345 20.6755C8.73782 20.6755 8.83218 20.6755 8.87621 20.6755C9.05235 21.1316 9.21591 21.557 9.37947 21.9885C9.33543 21.9885 9.26624 21.9946 9.19704 21.9946C7.13368 21.9946 5.07032 22.0008 3.00068 21.9946C1.51606 21.9885 0.333408 21.0576 0.0566165 19.6645C-0.00629073 19.3563 0.00629072 19.0357 0.00629072 18.7213C0.00629072 13.4816 0.00629072 8.24808 0 3.00836C0 1.86179 0.478095 0.974115 1.48461 0.376171C1.98158 0.0987743 2.51629 -0.0306776 3.04471 0.0124731Z"
                                fill="var(--font-color1)" />
                              <path
                                d="M19.6212 17.1801C19.6149 14.9363 17.5956 13.1178 15.2303 13.2719C12.6826 13.4383 11.3678 15.6452 11.4433 17.1739C11.8711 17.1739 12.3051 17.1739 12.7329 17.1739C13.7205 17.1801 14.4062 17.8397 14.4251 18.8013C14.4314 19.3253 14.4377 19.8493 14.4251 20.3733C14.4062 21.3164 13.7331 21.976 12.7706 21.9945C12.3554 22.0007 11.9403 22.0068 11.5251 21.9822C10.8016 21.939 10.1034 21.3287 10.0908 20.626C10.0593 19.2513 9.99642 17.8644 10.1411 16.502C10.399 14.1041 12.4813 12.2055 14.9158 11.9589C17.7843 11.6753 20.3132 13.4877 20.8857 16.2493C20.9611 16.613 20.9863 16.9952 20.9926 17.3712C21.0052 18.3328 20.9989 19.2945 20.9989 20.2561C20.9989 21.3226 20.3195 21.9883 19.2375 21.9945C18.8852 21.9945 18.5329 22.0007 18.1806 21.9883C17.3125 21.9513 16.6457 21.2794 16.6268 20.4349C16.6143 19.8678 16.6143 19.2945 16.6268 18.7274C16.6457 17.8644 17.344 17.1863 18.2247 17.1739C18.6776 17.1678 19.1431 17.1801 19.6212 17.1801Z"
                                fill="var(--font-color1)" />
                            </svg>
                            <svg class="emty_l_hover" width="21" height="22" viewBox="0 0 21 22" fill="none"
                              xmlns="http://www.w3.org/2000/svg">
                              <path
                                d="M19.4948 12.1068C17.2112 10.6027 14.8396 10.3315 12.4366 11.589C10.0398 12.8404 8.93264 14.9055 8.90748 17.5685C8.81941 17.5685 8.74392 17.5685 8.66843 17.5685C7.42916 17.5685 6.18989 17.5685 4.95061 17.5685C4.36558 17.5685 4.12024 17.3342 4.12024 16.7548C4.12024 11.4411 4.12024 6.12739 4.12024 0.813697C4.11395 0.234246 4.35299 0 4.94432 0C8.97039 0 12.9964 0 17.0225 0C18.4945 0 19.4948 0.9863 19.4948 2.4226C19.4948 5.57259 19.4948 8.72875 19.4948 11.8787C19.4948 11.9466 19.4948 12.0144 19.4948 12.1068ZM11.8201 6.20136C12.9021 6.20136 13.9841 6.20136 15.0661 6.20136C15.5316 6.20136 15.8399 5.94862 15.8461 5.5541C15.8524 5.16574 15.5442 4.87602 15.0913 4.87602C12.9021 4.86985 10.7129 4.87602 8.52374 4.87602C8.39164 4.87602 8.25324 4.90684 8.14001 4.95616C7.85693 5.08561 7.71224 5.39999 7.78773 5.68972C7.86322 5.99177 8.12114 6.20136 8.45455 6.20136C9.5743 6.20136 10.694 6.20136 11.8201 6.20136ZM11.8201 8.96916C12.3359 8.96916 12.8518 8.96916 13.3739 8.96916C13.808 8.96916 14.1225 8.69177 14.1225 8.30341C14.1225 7.92122 13.8142 7.63766 13.3739 7.63766C12.3296 7.63149 11.2917 7.63149 10.2474 7.63766C9.80076 7.63766 9.51139 7.91506 9.51139 8.30341C9.51139 8.69177 9.81334 8.963 10.2537 8.963C10.7695 8.97533 11.2917 8.96916 11.8201 8.96916Z"
                                fill="var(--text-color)" />
                              <path
                                d="M3.04471 0.0124731C3.02584 0.406993 2.98809 0.789184 2.98809 1.17138C2.9818 6.27548 2.9818 11.3857 2.98809 16.4898C2.98809 16.8412 3.03213 17.1926 3.051 17.5563C2.99438 17.5624 2.89373 17.5748 2.79308 17.5809C1.90609 17.6549 1.39025 18.407 1.36509 19.1837C1.33992 19.9172 1.98787 20.5707 2.7805 20.657C2.90631 20.6693 3.03213 20.6755 3.15794 20.6755C4.98854 20.6755 6.81914 20.6755 8.64345 20.6755C8.73782 20.6755 8.83218 20.6755 8.87621 20.6755C9.05235 21.1316 9.21591 21.557 9.37947 21.9885C9.33543 21.9885 9.26624 21.9946 9.19704 21.9946C7.13368 21.9946 5.07032 22.0008 3.00068 21.9946C1.51606 21.9885 0.333408 21.0576 0.0566165 19.6645C-0.00629073 19.3563 0.00629072 19.0357 0.00629072 18.7213C0.00629072 13.4816 0.00629072 8.24808 0 3.00836C0 1.86179 0.478095 0.974115 1.48461 0.376171C1.98158 0.0987743 2.51629 -0.0306776 3.04471 0.0124731Z"
                                fill="var(--text-color)" />
                              <path
                                d="M19.6212 17.1801C19.6149 14.9363 17.5956 13.1178 15.2303 13.2719C12.6826 13.4383 11.3678 15.6452 11.4433 17.1739C11.8711 17.1739 12.3051 17.1739 12.7329 17.1739C13.7205 17.1801 14.4062 17.8397 14.4251 18.8013C14.4314 19.3253 14.4377 19.8493 14.4251 20.3733C14.4062 21.3164 13.7331 21.976 12.7706 21.9945C12.3554 22.0007 11.9403 22.0068 11.5251 21.9822C10.8016 21.939 10.1034 21.3287 10.0908 20.626C10.0593 19.2513 9.99642 17.8644 10.1411 16.502C10.399 14.1041 12.4813 12.2055 14.9158 11.9589C17.7843 11.6753 20.3132 13.4877 20.8857 16.2493C20.9611 16.613 20.9863 16.9952 20.9926 17.3712C21.0052 18.3328 20.9989 19.2945 20.9989 20.2561C20.9989 21.3226 20.3195 21.9883 19.2375 21.9945C18.8852 21.9945 18.5329 22.0007 18.1806 21.9883C17.3125 21.9513 16.6457 21.2794 16.6268 20.4349C16.6143 19.8678 16.6143 19.2945 16.6268 18.7274C16.6457 17.8644 17.344 17.1863 18.2247 17.1739C18.6776 17.1678 19.1431 17.1801 19.6212 17.1801Z"
                                fill="var(--text-color)" />
                            </svg>
                          </div>
                          <p class="show_p_md">
                            слушать
                          </p>
                          <div class="on_hov shrt">
                            слушать
                          </div>
                        </button>
                        <div class="flex items-center cursor-pointer icons_w fav_hov"
                          [ngClass]="{'in-favourites': item.inFavourites}" (click)="favorites(item)">
                          <div class="icon-wrap star_w">
                            <svg class="emty_f" width="25" height="24" viewBox="0 0 25 24" fill="none"
                              xmlns="http://www.w3.org/2000/svg">
                              <path
                                d="M5.96868 23.9618C5.94253 23.9618 5.91638 23.9618 5.89022 23.9618C5.36715 23.9356 4.89638 23.6999 4.53023 23.3333C4.16408 22.9143 3.98101 22.3906 4.03332 21.8145C4.05947 21.5789 4.11178 21.317 4.19024 21.0552C4.71331 19.4055 5.41946 17.2844 6.1256 15.1372C4.42562 13.9065 2.72564 12.6758 1.02565 11.4189C0.816424 11.2618 0.162584 10.7904 0.0318159 9.95249C-0.0727985 9.35022 0.0841231 8.77414 0.450273 8.35517C0.842577 7.88383 1.44411 7.62197 2.15026 7.62197C2.90871 7.62197 3.66717 7.62197 4.42562 7.62197H8.55789C8.68866 7.25537 8.79327 6.88878 8.92404 6.52218C9.08096 6.05084 9.21173 5.60568 9.36865 5.13435C9.7348 4.00837 10.0748 2.8562 10.4671 1.73022C10.5979 1.31125 10.8333 0.918472 11.1209 0.604245C11.4871 0.211462 12.0363 -0.0242083 12.5855 0.00197721C13.1348 0.0281628 13.6578 0.290018 14.024 0.735173C14.2332 1.02321 14.4163 1.33744 14.5471 1.70404C15.044 3.24899 15.5409 4.79393 16.0378 6.33888L16.4563 7.62197H17.6593C19.3332 7.62197 21.007 7.62197 22.6808 7.62197C23.047 7.62197 23.387 7.67434 23.7008 7.77909C24.2762 7.98857 24.7208 8.43372 24.9039 9.00981C25.087 9.58589 25.0085 10.1882 24.6685 10.7119C24.4854 10.9999 24.25 11.2356 23.9624 11.4451C22.2624 12.6758 20.5624 13.9327 18.8886 15.1372C19.5424 17.1273 20.1962 19.0912 20.8239 21.0813C21.0593 21.7884 21.0332 22.3906 20.7455 22.9405C20.327 23.7785 19.3593 24.1974 18.4701 23.9094C18.1301 23.8046 17.8163 23.6475 17.5286 23.438C16.1424 22.443 14.7563 21.4218 13.344 20.4005L12.5071 19.7983L11.5132 20.5314C10.1794 21.5003 8.84558 22.4954 7.51174 23.4642C6.96252 23.7785 6.4656 23.9618 5.96868 23.9618ZM3.45794 9.14073C3.01333 9.14073 2.59487 9.14073 2.15026 9.14073C1.91487 9.14073 1.7318 9.21929 1.65334 9.32403C1.57488 9.40259 1.54872 9.53352 1.57488 9.66445C1.60103 9.76919 1.65334 9.90012 1.94103 10.1096C3.71947 11.3927 5.47176 12.702 7.25021 13.9851C7.69482 14.3255 7.85174 14.7968 7.69482 15.3205C6.96252 17.5463 6.23022 19.7983 5.68099 21.5003C5.62869 21.6312 5.60253 21.7884 5.60253 21.8931C5.60253 22.0502 5.62869 22.155 5.70715 22.2597C5.75945 22.3383 5.86407 22.3644 5.94253 22.3644C6.0733 22.3644 6.23022 22.3383 6.54406 22.1026C7.87789 21.1337 9.21173 20.1387 10.5456 19.1698L12.4809 17.7558L14.2594 19.0389C15.6455 20.0601 17.0317 21.0552 18.4178 22.0764C18.5747 22.1811 18.7316 22.2597 18.9147 22.3121C19.0716 22.3644 19.2286 22.2859 19.307 22.1288C19.3593 22.024 19.4116 21.8407 19.2809 21.4741C18.6009 19.4055 17.9209 17.3368 17.2409 15.2682C17.0578 14.7183 17.2147 14.2731 17.6593 13.9327C19.4116 12.6758 21.1901 11.3665 22.9424 10.0834C23.0731 9.97867 23.1777 9.87393 23.2824 9.743C23.3608 9.63826 23.387 9.50733 23.3347 9.40259C23.3085 9.29785 23.2039 9.21929 23.0993 9.16692C22.9685 9.11455 22.7854 9.08836 22.6024 9.08836C20.9285 9.08836 19.2547 9.08836 17.5809 9.08836H15.2794L15.0963 8.59084C15.0701 8.51228 15.044 8.45991 15.0178 8.40754L14.4948 6.78404C13.9978 5.23909 13.5009 3.69414 13.004 2.14919C12.9517 1.96589 12.8471 1.78259 12.7425 1.65167C12.664 1.54692 12.5332 1.52074 12.4809 1.52074C12.4286 1.52074 12.324 1.52074 12.2455 1.62548C12.1148 1.75641 12.0102 1.96589 11.9317 2.17538C11.5656 3.30136 11.1994 4.45352 10.8333 5.55331C10.6763 6.02465 10.5456 6.46981 10.3886 6.94115C10.2579 7.30775 10.1533 7.70053 10.0225 8.06713L9.68249 9.14073H4.42562C4.11178 9.14073 3.77178 9.14073 3.45794 9.14073Z"
                                fill="var(--font-color1)" />
                            </svg>
                            <svg class="emty_f_hover" width="25" height="24" viewBox="0 0 25 24" fill="none"
                              xmlns="http://www.w3.org/2000/svg">
                              <path
                                d="M5.96868 23.9618C5.94253 23.9618 5.91638 23.9618 5.89022 23.9618C5.36715 23.9356 4.89638 23.6999 4.53023 23.3333C4.16408 22.9143 3.98101 22.3906 4.03332 21.8145C4.05947 21.5789 4.11178 21.317 4.19024 21.0552C4.71331 19.4055 5.41946 17.2844 6.1256 15.1372C4.42562 13.9065 2.72564 12.6758 1.02565 11.4189C0.816424 11.2618 0.162584 10.7904 0.0318159 9.95249C-0.0727985 9.35022 0.0841231 8.77414 0.450273 8.35517C0.842577 7.88383 1.44411 7.62197 2.15026 7.62197C2.90871 7.62197 3.66717 7.62197 4.42562 7.62197H8.55789C8.68866 7.25537 8.79327 6.88878 8.92404 6.52218C9.08096 6.05084 9.21173 5.60568 9.36865 5.13435C9.7348 4.00837 10.0748 2.8562 10.4671 1.73022C10.5979 1.31125 10.8333 0.918472 11.1209 0.604245C11.4871 0.211462 12.0363 -0.0242083 12.5855 0.00197721C13.1348 0.0281628 13.6578 0.290018 14.024 0.735173C14.2332 1.02321 14.4163 1.33744 14.5471 1.70404C15.044 3.24899 15.5409 4.79393 16.0378 6.33888L16.4563 7.62197H17.6593C19.3332 7.62197 21.007 7.62197 22.6808 7.62197C23.047 7.62197 23.387 7.67434 23.7008 7.77909C24.2762 7.98857 24.7208 8.43372 24.9039 9.00981C25.087 9.58589 25.0085 10.1882 24.6685 10.7119C24.4854 10.9999 24.25 11.2356 23.9624 11.4451C22.2624 12.6758 20.5624 13.9327 18.8886 15.1372C19.5424 17.1273 20.1962 19.0912 20.8239 21.0813C21.0593 21.7884 21.0332 22.3906 20.7455 22.9405C20.327 23.7785 19.3593 24.1974 18.4701 23.9094C18.1301 23.8046 17.8163 23.6475 17.5286 23.438C16.1424 22.443 14.7563 21.4218 13.344 20.4005L12.5071 19.7983L11.5132 20.5314C10.1794 21.5003 8.84558 22.4954 7.51174 23.4642C6.96252 23.7785 6.4656 23.9618 5.96868 23.9618ZM3.45794 9.14073C3.01333 9.14073 2.59487 9.14073 2.15026 9.14073C1.91487 9.14073 1.7318 9.21929 1.65334 9.32403C1.57488 9.40259 1.54872 9.53352 1.57488 9.66445C1.60103 9.76919 1.65334 9.90012 1.94103 10.1096C3.71947 11.3927 5.47176 12.702 7.25021 13.9851C7.69482 14.3255 7.85174 14.7968 7.69482 15.3205C6.96252 17.5463 6.23022 19.7983 5.68099 21.5003C5.62869 21.6312 5.60253 21.7884 5.60253 21.8931C5.60253 22.0502 5.62869 22.155 5.70715 22.2597C5.75945 22.3383 5.86407 22.3644 5.94253 22.3644C6.0733 22.3644 6.23022 22.3383 6.54406 22.1026C7.87789 21.1337 9.21173 20.1387 10.5456 19.1698L12.4809 17.7558L14.2594 19.0389C15.6455 20.0601 17.0317 21.0552 18.4178 22.0764C18.5747 22.1811 18.7316 22.2597 18.9147 22.3121C19.0716 22.3644 19.2286 22.2859 19.307 22.1288C19.3593 22.024 19.4116 21.8407 19.2809 21.4741C18.6009 19.4055 17.9209 17.3368 17.2409 15.2682C17.0578 14.7183 17.2147 14.2731 17.6593 13.9327C19.4116 12.6758 21.1901 11.3665 22.9424 10.0834C23.0731 9.97867 23.1777 9.87393 23.2824 9.743C23.3608 9.63826 23.387 9.50733 23.3347 9.40259C23.3085 9.29785 23.2039 9.21929 23.0993 9.16692C22.9685 9.11455 22.7854 9.08836 22.6024 9.08836C20.9285 9.08836 19.2547 9.08836 17.5809 9.08836H15.2794L15.0963 8.59084C15.0701 8.51228 15.044 8.45991 15.0178 8.40754L14.4948 6.78404C13.9978 5.23909 13.5009 3.69414 13.004 2.14919C12.9517 1.96589 12.8471 1.78259 12.7425 1.65167C12.664 1.54692 12.5332 1.52074 12.4809 1.52074C12.4286 1.52074 12.324 1.52074 12.2455 1.62548C12.1148 1.75641 12.0102 1.96589 11.9317 2.17538C11.5656 3.30136 11.1994 4.45352 10.8333 5.55331C10.6763 6.02465 10.5456 6.46981 10.3886 6.94115C10.2579 7.30775 10.1533 7.70053 10.0225 8.06713L9.68249 9.14073H4.42562C4.11178 9.14073 3.77178 9.14073 3.45794 9.14073Z"
                                fill="var(--text-color)" />
                            </svg>
                          </div>
                          <p class="show_p_md">
                            добавить в избранное
                          </p>
                          <div class="on_hov">
                            добавить в избранное
                          </div>
                        </div>
                        <div class="flex items-center cursor-pointer icons_w shr_hov" *ngIf="profileService.profile"
                          (click)="share(item)">
                          <div class="icon-wrap share_w">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                              xmlns="http://www.w3.org/2000/svg">
                              <path
                                d="M15.9238 7.01009C13.5066 8.21941 11.0965 9.42157 8.66504 10.638C8.9368 11.5325 8.94395 12.427 8.66504 13.3357C11.0822 14.5451 13.4923 15.7544 15.8737 16.9422C16.2456 16.5988 16.5745 16.2267 16.9607 15.9476C19.6353 13.9941 23.4542 15.6113 23.9334 18.8957C24.3481 21.7509 22.0168 24.2268 19.1347 23.9835C17.0894 23.8117 15.3731 22.1516 15.1514 20.1051C15.1085 19.6829 15.1156 19.2464 15.1729 18.8313C15.2086 18.588 15.1442 18.5093 14.9512 18.4163C12.8772 17.3859 10.8105 16.3483 8.73656 15.3107C8.50056 15.1891 8.25741 15.0817 8.02141 14.9458C7.87838 14.8671 7.79972 14.8885 7.6853 15.0102C6.76276 15.9834 5.63283 16.4556 4.28836 16.4413C2.21445 16.4127 0.326467 14.7454 0.0475605 12.6846C-0.288557 10.2588 1.18464 8.12638 3.58752 7.63979C5.16084 7.32494 6.52676 7.79722 7.65669 8.94213C7.80687 9.09241 7.89984 9.10672 8.07862 9.01369C10.3742 7.85447 12.677 6.70955 14.9798 5.56464C15.1585 5.47877 15.2158 5.40005 15.1729 5.18538C14.7295 2.75959 16.4458 0.39105 18.8773 0.047575C21.4232 -0.317367 23.7117 1.45725 23.9763 3.99753C24.2266 6.37323 22.4673 8.57004 20.093 8.8348C18.4196 9.028 17.0751 8.41977 16.0239 7.12458C15.9881 7.09596 15.9595 7.06018 15.9238 7.01009ZM7.14179 11.9976C7.14179 10.5021 5.91174 9.26414 4.42424 9.2713C2.94389 9.27845 1.721 10.4949 1.70669 11.9762C1.69239 13.4574 2.92244 14.7025 4.41709 14.7168C5.90459 14.7311 7.14179 13.5003 7.14179 11.9976ZM16.832 19.5541C16.8248 21.0496 18.0549 22.2876 19.5424 22.2804C21.0227 22.2804 22.2671 21.0353 22.2671 19.5541C22.2599 18.0728 21.0513 16.8635 19.5567 16.8492C18.062 16.8277 16.8391 18.0442 16.832 19.5541ZM19.5424 7.14605C21.037 7.14605 22.2671 5.91527 22.2671 4.42688C22.2599 2.93849 21.0156 1.70055 19.5352 1.7077C18.0477 1.71486 16.8463 2.93133 16.832 4.41972C16.8248 5.92242 18.0406 7.14605 19.5424 7.14605Z"
                                fill="var(--font-color1)" />
                            </svg>
                          </div>
                          <p class="show_p_md">
                            поделиться
                          </p>
                          <div class="on_hov">
                            поделиться
                          </div>
                        </div>
                        <button class="flex items-center icons_w lik_hov" [ngClass]="{'is-liked': item.liked}"
                          (click)="likeLibrary(item)">
                          <div class="icon-wrap like_w">
                            <svg class="emty_l" width="24" height="22" viewBox="0 0 24 22" fill="none"
                              xmlns="http://www.w3.org/2000/svg">
                              <path
                                d="M11.7534 2.35249C11.576 2.19912 11.4123 2.05272 11.2486 1.89935C10.048 0.749078 8.90875 0.163484 7.67404 0.0589139C7.44892 0.0379998 7.22381 0.0310285 7.00552 0.0310285C4.87718 0.0310285 3.11038 0.909419 1.72559 2.65923C0.286236 4.47875 -0.252671 6.65381 0.108874 9.12864C0.402203 11.1294 1.2549 12.7816 2.63969 14.0365C4.74757 15.9397 6.91684 17.8428 9.01789 19.6763C9.79555 20.3595 10.5732 21.0357 11.3509 21.7189C11.4668 21.8235 11.5896 21.9002 11.7056 21.942C12.0399 22.0675 12.3468 21.9908 12.6674 21.698C13.8612 20.6453 15.055 19.5926 16.2556 18.54C17.0742 17.815 17.8996 17.0969 18.7182 16.3719C19.0047 16.1209 19.2981 15.8769 19.5777 15.6329C20.2258 15.0822 20.8943 14.5175 21.4946 13.8971C23.6229 11.6802 24.4006 9.03105 23.8071 6.03336C23.3023 3.49579 21.9517 1.69718 19.796 0.679364C17.7291 -0.296625 15.7713 -0.21994 13.9908 0.902448C13.3223 1.32073 12.7016 1.87844 12.1422 2.55466L12.074 2.63831L11.9989 2.5686C11.9171 2.49192 11.8352 2.4222 11.7534 2.35249ZM12.7561 4.47875C12.8175 4.38812 12.8789 4.2975 12.9403 4.20687C13.1586 3.87922 13.3837 3.54459 13.6566 3.25877C15.13 1.69021 17.4426 1.39044 19.4072 2.52677C20.8602 3.36334 21.7538 4.69486 22.0744 6.47953C22.5315 9.0659 21.7675 11.2689 19.7892 13.0187C18.0224 14.5802 16.2215 16.1697 14.4752 17.6964C13.7316 18.3517 12.9881 19.0001 12.2513 19.6554C12.2172 19.6902 12.1763 19.7181 12.1354 19.753L11.9989 19.8645L8.01512 16.3579C7.59218 15.9815 7.15559 15.605 6.73265 15.2425C5.79809 14.4338 4.82943 13.5973 3.90851 12.7328C2.5783 11.485 1.8211 9.77698 1.78699 7.92957C1.75288 6.17976 2.37365 4.51361 3.49921 3.36334C5.02043 1.80872 7.43528 1.39741 9.23618 2.38037C9.94563 2.77077 10.5459 3.35636 11.0576 4.17201C11.0848 4.21384 11.1121 4.25567 11.1326 4.2975C11.1667 4.36024 11.2008 4.41601 11.2417 4.47178C11.4191 4.73669 11.692 4.89703 11.9853 4.89703H11.9921C12.2923 4.904 12.5719 4.75063 12.7561 4.47875Z"
                                fill="var(--font-color1)" />
                            </svg>
                            <svg class="emty_l_hover" width="24" height="22" viewBox="0 0 24 22" fill="none"
                              xmlns="http://www.w3.org/2000/svg">
                              <path
                                d="M11.7534 2.35249C11.576 2.19912 11.4123 2.05272 11.2486 1.89935C10.048 0.749078 8.90875 0.163484 7.67404 0.0589139C7.44892 0.0379998 7.22381 0.0310285 7.00552 0.0310285C4.87718 0.0310285 3.11038 0.909419 1.72559 2.65923C0.286236 4.47875 -0.252671 6.65381 0.108874 9.12864C0.402203 11.1294 1.2549 12.7816 2.63969 14.0365C4.74757 15.9397 6.91684 17.8428 9.01789 19.6763C9.79555 20.3595 10.5732 21.0357 11.3509 21.7189C11.4668 21.8235 11.5896 21.9002 11.7056 21.942C12.0399 22.0675 12.3468 21.9908 12.6674 21.698C13.8612 20.6453 15.055 19.5926 16.2556 18.54C17.0742 17.815 17.8996 17.0969 18.7182 16.3719C19.0047 16.1209 19.2981 15.8769 19.5777 15.6329C20.2258 15.0822 20.8943 14.5175 21.4946 13.8971C23.6229 11.6802 24.4006 9.03105 23.8071 6.03336C23.3023 3.49579 21.9517 1.69718 19.796 0.679364C17.7291 -0.296625 15.7713 -0.21994 13.9908 0.902448C13.3223 1.32073 12.7016 1.87844 12.1422 2.55466L12.074 2.63831L11.9989 2.5686C11.9171 2.49192 11.8352 2.4222 11.7534 2.35249ZM12.7561 4.47875C12.8175 4.38812 12.8789 4.2975 12.9403 4.20687C13.1586 3.87922 13.3837 3.54459 13.6566 3.25877C15.13 1.69021 17.4426 1.39044 19.4072 2.52677C20.8602 3.36334 21.7538 4.69486 22.0744 6.47953C22.5315 9.0659 21.7675 11.2689 19.7892 13.0187C18.0224 14.5802 16.2215 16.1697 14.4752 17.6964C13.7316 18.3517 12.9881 19.0001 12.2513 19.6554C12.2172 19.6902 12.1763 19.7181 12.1354 19.753L11.9989 19.8645L8.01512 16.3579C7.59218 15.9815 7.15559 15.605 6.73265 15.2425C5.79809 14.4338 4.82943 13.5973 3.90851 12.7328C2.5783 11.485 1.8211 9.77698 1.78699 7.92957C1.75288 6.17976 2.37365 4.51361 3.49921 3.36334C5.02043 1.80872 7.43528 1.39741 9.23618 2.38037C9.94563 2.77077 10.5459 3.35636 11.0576 4.17201C11.0848 4.21384 11.1121 4.25567 11.1326 4.2975C11.1667 4.36024 11.2008 4.41601 11.2417 4.47178C11.4191 4.73669 11.692 4.89703 11.9853 4.89703H11.9921C12.2923 4.904 12.5719 4.75063 12.7561 4.47875Z"
                                fill="var(--text-color)" />
                            </svg>
                          </div>
                          <p class="show_p_md">
                            мне нравится
                          </p>
                          <span class="ml-2 text-color default_">
                            {{item.likes}}
                          </span>
                          <div class="on_hov">
                            мне нравится
                          </div>
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="invis_part">
                  <div class="flex">
                    <div class="flex flex-col icons_w b_p">
                      <div class="tr_descr">{{item.annotation}}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            }
            <!-- @else {
            <div class="search-title" (click)="followLink(item)" [innerHTML]="format(item?.title || 'Без названия')">
            </div>
            } -->
            <!-- <div class="search-description"
              [innerHTML]="format(item.processedContent || getContent(item) || item.content)"></div> -->
          </div>
          <!-- @if(type == 'forum') {
          <ng-container *ngTemplateOutlet="topicItem; context: {topic: item}"></ng-container>
          } -->
          }
        </div>

        <!-- Pages Tab -->
        <div class="search-content" *ngIf="activeTab === 'Статьи'">
          <div>
            @for(content of sort(items); track content.id; let i = $index; let last = $last) {
            <div [ngClass]="{'widen': isOpened_[i], 'last': last}" class="article-item m">
              <div class="vis_part relative">
                <div (click)="isOpened_[i] = !isOpened_[i]" class="art_img_"></div>
                <div (click)="isOpened_[i] = !isOpened_[i]" class="art_img">
                  @if(content.preview) {
                  <img style="object-fit: cover" width="66" height="66"
                    [ngSrc]="environment.serverUrl + '/upload/' + content.preview.name">
                  } @else {
                  <img src="assets/images/clouds.webp" alt="image">
                  }
                </div>
                <div class="flex justify-between">
                  <div class="titl_w">
                    <div class="article-title" (click)="$event.stopPropagation();navigate(content)">{{content.title}}
                    </div>
                    <div class="article-category">{{ content.author || content.title}}</div>
                  </div>
                  <div class="actions_w" (click)="showMenu(i, $event)" [class.show_menu]="show_menu[i]">
                    <div class="flex items-center cursor-pointer icons_w show_md">
                      <div class="icon-wrap star_w no_h">
                        <svg width="7" height="22" viewBox="0 0 7 22" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <circle cx="3.27778" cy="3.02778" r="2.27778" stroke="var(--text-color)" />
                          <circle cx="3.27778" cy="10.9999" r="2.27778" stroke="var(--text-color)" />
                          <circle cx="3.27778" cy="18.9721" r="2.27778" stroke="var(--text-color)" />
                        </svg>
                      </div>
                    </div>
                    <div class="md_chg">
                      <div title="Добавить в очередь" class="flex items-center cursor-pointer icons_w fav_hov"
                        [ngClass]="{'in-favourites': content.inFavourites}"
                        (click)="favoritesArticle(content);$event.stopPropagation();">
                        <div class="icon-wrap star_w favr_">
                          <svg class="emty_f" width="25" height="24" viewBox="0 0 25 24" fill="none"
                            xmlns="http://www.w3.org/2000/svg">
                            <path
                              d="M5.96868 23.9618C5.94253 23.9618 5.91638 23.9618 5.89022 23.9618C5.36715 23.9356 4.89638 23.6999 4.53023 23.3333C4.16408 22.9143 3.98101 22.3906 4.03332 21.8145C4.05947 21.5789 4.11178 21.317 4.19024 21.0552C4.71331 19.4055 5.41946 17.2844 6.1256 15.1372C4.42562 13.9065 2.72564 12.6758 1.02565 11.4189C0.816424 11.2618 0.162584 10.7904 0.0318159 9.95249C-0.0727985 9.35022 0.0841231 8.77414 0.450273 8.35517C0.842577 7.88383 1.44411 7.62197 2.15026 7.62197C2.90871 7.62197 3.66717 7.62197 4.42562 7.62197H8.55789C8.68866 7.25537 8.79327 6.88878 8.92404 6.52218C9.08096 6.05084 9.21173 5.60568 9.36865 5.13435C9.7348 4.00837 10.0748 2.8562 10.4671 1.73022C10.5979 1.31125 10.8333 0.918472 11.1209 0.604245C11.4871 0.211462 12.0363 -0.0242083 12.5855 0.00197721C13.1348 0.0281628 13.6578 0.290018 14.024 0.735173C14.2332 1.02321 14.4163 1.33744 14.5471 1.70404C15.044 3.24899 15.5409 4.79393 16.0378 6.33888L16.4563 7.62197H17.6593C19.3332 7.62197 21.007 7.62197 22.6808 7.62197C23.047 7.62197 23.387 7.67434 23.7008 7.77909C24.2762 7.98857 24.7208 8.43372 24.9039 9.00981C25.087 9.58589 25.0085 10.1882 24.6685 10.7119C24.4854 10.9999 24.25 11.2356 23.9624 11.4451C22.2624 12.6758 20.5624 13.9327 18.8886 15.1372C19.5424 17.1273 20.1962 19.0912 20.8239 21.0813C21.0593 21.7884 21.0332 22.3906 20.7455 22.9405C20.327 23.7785 19.3593 24.1974 18.4701 23.9094C18.1301 23.8046 17.8163 23.6475 17.5286 23.438C16.1424 22.443 14.7563 21.4218 13.344 20.4005L12.5071 19.7983L11.5132 20.5314C10.1794 21.5003 8.84558 22.4954 7.51174 23.4642C6.96252 23.7785 6.4656 23.9618 5.96868 23.9618ZM3.45794 9.14073C3.01333 9.14073 2.59487 9.14073 2.15026 9.14073C1.91487 9.14073 1.7318 9.21929 1.65334 9.32403C1.57488 9.40259 1.54872 9.53352 1.57488 9.66445C1.60103 9.76919 1.65334 9.90012 1.94103 10.1096C3.71947 11.3927 5.47176 12.702 7.25021 13.9851C7.69482 14.3255 7.85174 14.7968 7.69482 15.3205C6.96252 17.5463 6.23022 19.7983 5.68099 21.5003C5.62869 21.6312 5.60253 21.7884 5.60253 21.8931C5.60253 22.0502 5.62869 22.155 5.70715 22.2597C5.75945 22.3383 5.86407 22.3644 5.94253 22.3644C6.0733 22.3644 6.23022 22.3383 6.54406 22.1026C7.87789 21.1337 9.21173 20.1387 10.5456 19.1698L12.4809 17.7558L14.2594 19.0389C15.6455 20.0601 17.0317 21.0552 18.4178 22.0764C18.5747 22.1811 18.7316 22.2597 18.9147 22.3121C19.0716 22.3644 19.2286 22.2859 19.307 22.1288C19.3593 22.024 19.4116 21.8407 19.2809 21.4741C18.6009 19.4055 17.9209 17.3368 17.2409 15.2682C17.0578 14.7183 17.2147 14.2731 17.6593 13.9327C19.4116 12.6758 21.1901 11.3665 22.9424 10.0834C23.0731 9.97867 23.1777 9.87393 23.2824 9.743C23.3608 9.63826 23.387 9.50733 23.3347 9.40259C23.3085 9.29785 23.2039 9.21929 23.0993 9.16692C22.9685 9.11455 22.7854 9.08836 22.6024 9.08836C20.9285 9.08836 19.2547 9.08836 17.5809 9.08836H15.2794L15.0963 8.59084C15.0701 8.51228 15.044 8.45991 15.0178 8.40754L14.4948 6.78404C13.9978 5.23909 13.5009 3.69414 13.004 2.14919C12.9517 1.96589 12.8471 1.78259 12.7425 1.65167C12.664 1.54692 12.5332 1.52074 12.4809 1.52074C12.4286 1.52074 12.324 1.52074 12.2455 1.62548C12.1148 1.75641 12.0102 1.96589 11.9317 2.17538C11.5656 3.30136 11.1994 4.45352 10.8333 5.55331C10.6763 6.02465 10.5456 6.46981 10.3886 6.94115C10.2579 7.30775 10.1533 7.70053 10.0225 8.06713L9.68249 9.14073H4.42562C4.11178 9.14073 3.77178 9.14073 3.45794 9.14073Z"
                              fill="var(--font-color1)" />
                          </svg>
                          <svg class="emty_f_hover" width="25" height="24" viewBox="0 0 25 24" fill="none"
                            xmlns="http://www.w3.org/2000/svg">
                            <path
                              d="M5.96868 23.9618C5.94253 23.9618 5.91638 23.9618 5.89022 23.9618C5.36715 23.9356 4.89638 23.6999 4.53023 23.3333C4.16408 22.9143 3.98101 22.3906 4.03332 21.8145C4.05947 21.5789 4.11178 21.317 4.19024 21.0552C4.71331 19.4055 5.41946 17.2844 6.1256 15.1372C4.42562 13.9065 2.72564 12.6758 1.02565 11.4189C0.816424 11.2618 0.162584 10.7904 0.0318159 9.95249C-0.0727985 9.35022 0.0841231 8.77414 0.450273 8.35517C0.842577 7.88383 1.44411 7.62197 2.15026 7.62197C2.90871 7.62197 3.66717 7.62197 4.42562 7.62197H8.55789C8.68866 7.25537 8.79327 6.88878 8.92404 6.52218C9.08096 6.05084 9.21173 5.60568 9.36865 5.13435C9.7348 4.00837 10.0748 2.8562 10.4671 1.73022C10.5979 1.31125 10.8333 0.918472 11.1209 0.604245C11.4871 0.211462 12.0363 -0.0242083 12.5855 0.00197721C13.1348 0.0281628 13.6578 0.290018 14.024 0.735173C14.2332 1.02321 14.4163 1.33744 14.5471 1.70404C15.044 3.24899 15.5409 4.79393 16.0378 6.33888L16.4563 7.62197H17.6593C19.3332 7.62197 21.007 7.62197 22.6808 7.62197C23.047 7.62197 23.387 7.67434 23.7008 7.77909C24.2762 7.98857 24.7208 8.43372 24.9039 9.00981C25.087 9.58589 25.0085 10.1882 24.6685 10.7119C24.4854 10.9999 24.25 11.2356 23.9624 11.4451C22.2624 12.6758 20.5624 13.9327 18.8886 15.1372C19.5424 17.1273 20.1962 19.0912 20.8239 21.0813C21.0593 21.7884 21.0332 22.3906 20.7455 22.9405C20.327 23.7785 19.3593 24.1974 18.4701 23.9094C18.1301 23.8046 17.8163 23.6475 17.5286 23.438C16.1424 22.443 14.7563 21.4218 13.344 20.4005L12.5071 19.7983L11.5132 20.5314C10.1794 21.5003 8.84558 22.4954 7.51174 23.4642C6.96252 23.7785 6.4656 23.9618 5.96868 23.9618ZM3.45794 9.14073C3.01333 9.14073 2.59487 9.14073 2.15026 9.14073C1.91487 9.14073 1.7318 9.21929 1.65334 9.32403C1.57488 9.40259 1.54872 9.53352 1.57488 9.66445C1.60103 9.76919 1.65334 9.90012 1.94103 10.1096C3.71947 11.3927 5.47176 12.702 7.25021 13.9851C7.69482 14.3255 7.85174 14.7968 7.69482 15.3205C6.96252 17.5463 6.23022 19.7983 5.68099 21.5003C5.62869 21.6312 5.60253 21.7884 5.60253 21.8931C5.60253 22.0502 5.62869 22.155 5.70715 22.2597C5.75945 22.3383 5.86407 22.3644 5.94253 22.3644C6.0733 22.3644 6.23022 22.3383 6.54406 22.1026C7.87789 21.1337 9.21173 20.1387 10.5456 19.1698L12.4809 17.7558L14.2594 19.0389C15.6455 20.0601 17.0317 21.0552 18.4178 22.0764C18.5747 22.1811 18.7316 22.2597 18.9147 22.3121C19.0716 22.3644 19.2286 22.2859 19.307 22.1288C19.3593 22.024 19.4116 21.8407 19.2809 21.4741C18.6009 19.4055 17.9209 17.3368 17.2409 15.2682C17.0578 14.7183 17.2147 14.2731 17.6593 13.9327C19.4116 12.6758 21.1901 11.3665 22.9424 10.0834C23.0731 9.97867 23.1777 9.87393 23.2824 9.743C23.3608 9.63826 23.387 9.50733 23.3347 9.40259C23.3085 9.29785 23.2039 9.21929 23.0993 9.16692C22.9685 9.11455 22.7854 9.08836 22.6024 9.08836C20.9285 9.08836 19.2547 9.08836 17.5809 9.08836H15.2794L15.0963 8.59084C15.0701 8.51228 15.044 8.45991 15.0178 8.40754L14.4948 6.78404C13.9978 5.23909 13.5009 3.69414 13.004 2.14919C12.9517 1.96589 12.8471 1.78259 12.7425 1.65167C12.664 1.54692 12.5332 1.52074 12.4809 1.52074C12.4286 1.52074 12.324 1.52074 12.2455 1.62548C12.1148 1.75641 12.0102 1.96589 11.9317 2.17538C11.5656 3.30136 11.1994 4.45352 10.8333 5.55331C10.6763 6.02465 10.5456 6.46981 10.3886 6.94115C10.2579 7.30775 10.1533 7.70053 10.0225 8.06713L9.68249 9.14073H4.42562C4.11178 9.14073 3.77178 9.14073 3.45794 9.14073Z"
                              fill="var(--text-color)" />
                          </svg>
                        </div>
                        <p class="show_p_md cdf">
                          добавить в избранное
                        </p>
                        <div class="on_hov">
                          добавить в избранное
                        </div>
                      </div>
                      <div class="flex items-center cursor-pointer icons_w shr_hov"
                        (click)="shareArticle(content);$event.stopPropagation();">
                        <div class="icon-wrap share_w">
                          <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                            xmlns="http://www.w3.org/2000/svg">
                            <path
                              d="M15.9238 7.01009C13.5066 8.21941 11.0965 9.42157 8.66504 10.638C8.9368 11.5325 8.94395 12.427 8.66504 13.3357C11.0822 14.5451 13.4923 15.7544 15.8737 16.9422C16.2456 16.5988 16.5745 16.2267 16.9607 15.9476C19.6353 13.9941 23.4542 15.6113 23.9334 18.8957C24.3481 21.7509 22.0168 24.2268 19.1347 23.9835C17.0894 23.8117 15.3731 22.1516 15.1514 20.1051C15.1085 19.6829 15.1156 19.2464 15.1729 18.8313C15.2086 18.588 15.1442 18.5093 14.9512 18.4163C12.8772 17.3859 10.8105 16.3483 8.73656 15.3107C8.50056 15.1891 8.25741 15.0817 8.02141 14.9458C7.87838 14.8671 7.79972 14.8885 7.6853 15.0102C6.76276 15.9834 5.63283 16.4556 4.28836 16.4413C2.21445 16.4127 0.326467 14.7454 0.0475605 12.6846C-0.288557 10.2588 1.18464 8.12638 3.58752 7.63979C5.16084 7.32494 6.52676 7.79722 7.65669 8.94213C7.80687 9.09241 7.89984 9.10672 8.07862 9.01369C10.3742 7.85447 12.677 6.70955 14.9798 5.56464C15.1585 5.47877 15.2158 5.40005 15.1729 5.18538C14.7295 2.75959 16.4458 0.39105 18.8773 0.047575C21.4232 -0.317367 23.7117 1.45725 23.9763 3.99753C24.2266 6.37323 22.4673 8.57004 20.093 8.8348C18.4196 9.028 17.0751 8.41977 16.0239 7.12458C15.9881 7.09596 15.9595 7.06018 15.9238 7.01009ZM7.14179 11.9976C7.14179 10.5021 5.91174 9.26414 4.42424 9.2713C2.94389 9.27845 1.721 10.4949 1.70669 11.9762C1.69239 13.4574 2.92244 14.7025 4.41709 14.7168C5.90459 14.7311 7.14179 13.5003 7.14179 11.9976ZM16.832 19.5541C16.8248 21.0496 18.0549 22.2876 19.5424 22.2804C21.0227 22.2804 22.2671 21.0353 22.2671 19.5541C22.2599 18.0728 21.0513 16.8635 19.5567 16.8492C18.062 16.8277 16.8391 18.0442 16.832 19.5541ZM19.5424 7.14605C21.037 7.14605 22.2671 5.91527 22.2671 4.42688C22.2599 2.93849 21.0156 1.70055 19.5352 1.7077C18.0477 1.71486 16.8463 2.93133 16.832 4.41972C16.8248 5.92242 18.0406 7.14605 19.5424 7.14605Z"
                              fill="var(--font-color1)" />
                          </svg>
                        </div>
                        <p class="show_p_md">
                          поделиться
                        </p>
                        <div class="on_hov">
                          поделиться
                        </div>
                      </div>
                      <button class="flex items-center icons_w lik_hov" [ngClass]="{'is-liked': content.liked}"
                        (click)="likeArticle(content);$event.stopPropagation();">
                        <div class="icon-wrap like_w">
                          <svg class="emty_l" width="24" height="22" viewBox="0 0 24 22" fill="none"
                            xmlns="http://www.w3.org/2000/svg">
                            <path
                              d="M11.7534 2.35249C11.576 2.19912 11.4123 2.05272 11.2486 1.89935C10.048 0.749078 8.90875 0.163484 7.67404 0.0589139C7.44892 0.0379998 7.22381 0.0310285 7.00552 0.0310285C4.87718 0.0310285 3.11038 0.909419 1.72559 2.65923C0.286236 4.47875 -0.252671 6.65381 0.108874 9.12864C0.402203 11.1294 1.2549 12.7816 2.63969 14.0365C4.74757 15.9397 6.91684 17.8428 9.01789 19.6763C9.79555 20.3595 10.5732 21.0357 11.3509 21.7189C11.4668 21.8235 11.5896 21.9002 11.7056 21.942C12.0399 22.0675 12.3468 21.9908 12.6674 21.698C13.8612 20.6453 15.055 19.5926 16.2556 18.54C17.0742 17.815 17.8996 17.0969 18.7182 16.3719C19.0047 16.1209 19.2981 15.8769 19.5777 15.6329C20.2258 15.0822 20.8943 14.5175 21.4946 13.8971C23.6229 11.6802 24.4006 9.03105 23.8071 6.03336C23.3023 3.49579 21.9517 1.69718 19.796 0.679364C17.7291 -0.296625 15.7713 -0.21994 13.9908 0.902448C13.3223 1.32073 12.7016 1.87844 12.1422 2.55466L12.074 2.63831L11.9989 2.5686C11.9171 2.49192 11.8352 2.4222 11.7534 2.35249ZM12.7561 4.47875C12.8175 4.38812 12.8789 4.2975 12.9403 4.20687C13.1586 3.87922 13.3837 3.54459 13.6566 3.25877C15.13 1.69021 17.4426 1.39044 19.4072 2.52677C20.8602 3.36334 21.7538 4.69486 22.0744 6.47953C22.5315 9.0659 21.7675 11.2689 19.7892 13.0187C18.0224 14.5802 16.2215 16.1697 14.4752 17.6964C13.7316 18.3517 12.9881 19.0001 12.2513 19.6554C12.2172 19.6902 12.1763 19.7181 12.1354 19.753L11.9989 19.8645L8.01512 16.3579C7.59218 15.9815 7.15559 15.605 6.73265 15.2425C5.79809 14.4338 4.82943 13.5973 3.90851 12.7328C2.5783 11.485 1.8211 9.77698 1.78699 7.92957C1.75288 6.17976 2.37365 4.51361 3.49921 3.36334C5.02043 1.80872 7.43528 1.39741 9.23618 2.38037C9.94563 2.77077 10.5459 3.35636 11.0576 4.17201C11.0848 4.21384 11.1121 4.25567 11.1326 4.2975C11.1667 4.36024 11.2008 4.41601 11.2417 4.47178C11.4191 4.73669 11.692 4.89703 11.9853 4.89703H11.9921C12.2923 4.904 12.5719 4.75063 12.7561 4.47875Z"
                              fill="var(--font-color1)" />
                          </svg>
                          <svg class="emty_l_hover" width="24" height="22" viewBox="0 0 24 22" fill="none"
                            xmlns="http://www.w3.org/2000/svg">
                            <path
                              d="M11.7534 2.35249C11.576 2.19912 11.4123 2.05272 11.2486 1.89935C10.048 0.749078 8.90875 0.163484 7.67404 0.0589139C7.44892 0.0379998 7.22381 0.0310285 7.00552 0.0310285C4.87718 0.0310285 3.11038 0.909419 1.72559 2.65923C0.286236 4.47875 -0.252671 6.65381 0.108874 9.12864C0.402203 11.1294 1.2549 12.7816 2.63969 14.0365C4.74757 15.9397 6.91684 17.8428 9.01789 19.6763C9.79555 20.3595 10.5732 21.0357 11.3509 21.7189C11.4668 21.8235 11.5896 21.9002 11.7056 21.942C12.0399 22.0675 12.3468 21.9908 12.6674 21.698C13.8612 20.6453 15.055 19.5926 16.2556 18.54C17.0742 17.815 17.8996 17.0969 18.7182 16.3719C19.0047 16.1209 19.2981 15.8769 19.5777 15.6329C20.2258 15.0822 20.8943 14.5175 21.4946 13.8971C23.6229 11.6802 24.4006 9.03105 23.8071 6.03336C23.3023 3.49579 21.9517 1.69718 19.796 0.679364C17.7291 -0.296625 15.7713 -0.21994 13.9908 0.902448C13.3223 1.32073 12.7016 1.87844 12.1422 2.55466L12.074 2.63831L11.9989 2.5686C11.9171 2.49192 11.8352 2.4222 11.7534 2.35249ZM12.7561 4.47875C12.8175 4.38812 12.8789 4.2975 12.9403 4.20687C13.1586 3.87922 13.3837 3.54459 13.6566 3.25877C15.13 1.69021 17.4426 1.39044 19.4072 2.52677C20.8602 3.36334 21.7538 4.69486 22.0744 6.47953C22.5315 9.0659 21.7675 11.2689 19.7892 13.0187C18.0224 14.5802 16.2215 16.1697 14.4752 17.6964C13.7316 18.3517 12.9881 19.0001 12.2513 19.6554C12.2172 19.6902 12.1763 19.7181 12.1354 19.753L11.9989 19.8645L8.01512 16.3579C7.59218 15.9815 7.15559 15.605 6.73265 15.2425C5.79809 14.4338 4.82943 13.5973 3.90851 12.7328C2.5783 11.485 1.8211 9.77698 1.78699 7.92957C1.75288 6.17976 2.37365 4.51361 3.49921 3.36334C5.02043 1.80872 7.43528 1.39741 9.23618 2.38037C9.94563 2.77077 10.5459 3.35636 11.0576 4.17201C11.0848 4.21384 11.1121 4.25567 11.1326 4.2975C11.1667 4.36024 11.2008 4.41601 11.2417 4.47178C11.4191 4.73669 11.692 4.89703 11.9853 4.89703H11.9921C12.2923 4.904 12.5719 4.75063 12.7561 4.47875Z"
                              fill="var(--text-color)" />
                          </svg>
                        </div>
                        <p class="show_p_md">
                          мне нравится
                        </p>
                        <span class="ml-2 text-color default_">
                          {{content.likes}}
                        </span>

                        <div class="on_hov">
                          мне нравится
                        </div>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
              <div class="invis_part">
                <div class="article-content" [innerHTML]="content.content"></div>
                <p (click)="$event.stopPropagation();navigate(content)" class="link_more">
                  Читать далее
                </p>
                <div class="tags_cont">
                  @for (tag of content.tags; track $index) {
                  <!-- (click)="$event.preventDefault(); addTagFilter(tag)" -->
                  <a style="cursor: pointer;">
                    <div class="tag_item">{{tag.name}}</div>
                  </a>
                  }
                </div>
                <div class="flex">
                  <div class="flex items-center icons_w b_p">
                    <div class="icon-wrap clock_w">
                      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                          d="M11.25 0C11.75 0 12.25 0 12.75 0C12.8125 0.0125261 12.8687 0.0313152 12.9312 0.0375783C13.6812 0.169102 14.4625 0.212944 15.1812 0.438413C19.9188 1.8977 22.8062 5.06681 23.8125 9.93319C23.9 10.3653 23.9375 10.81 24 11.2484C24 11.7495 24 12.2505 24 12.7516C23.9875 12.8142 23.9688 12.8706 23.9625 12.9332C23.8375 14.5804 23.4187 16.1524 22.6125 17.5992C20.3438 21.6451 16.8875 23.8121 12.25 23.9937C9.75625 24.0939 7.44375 23.3987 5.36875 22.0021C2.54375 20.0856 0.8125 17.4489 0.1875 14.0919C0.10625 13.6472 0.0625 13.1962 0 12.7516C0 12.2505 0 11.7495 0 11.2484C0.0125 11.1795 0.03125 11.1106 0.0375 11.0418C0.11875 10.4969 0.16875 9.93946 0.28125 9.40084C1.25 4.74739 5.21875 0.951983 9.90625 0.187891C10.3562 0.112735 10.8 0.0626305 11.25 0ZM1.925 12.9395C2.375 18.0125 6.55 21.7328 11.0625 22.0647C11.0625 21.8017 11.0625 21.5386 11.0625 21.2693C11.0688 20.6931 11.4563 20.286 12 20.286C12.5437 20.286 12.9312 20.6931 12.9375 21.2693C12.9375 21.5324 12.9375 21.7954 12.9375 22.0647C17.7875 21.714 21.75 17.5741 22.0562 12.9395C21.9375 12.9395 21.825 12.9395 21.7062 12.9395C20.7125 12.9395 20.2812 12.6514 20.2812 11.9937C20.2875 11.3486 20.7125 11.0605 21.6938 11.0605C21.8188 11.0605 21.9375 11.0605 22.0625 11.0605C21.65 5.92484 17.2875 2.19207 12.9375 1.94781C12.9375 2.20459 12.9375 2.46138 12.9375 2.71816C12.9312 3.30689 12.5437 3.72025 11.9875 3.71399C11.45 3.70772 11.0625 3.30063 11.0625 2.71816C11.0625 2.45511 11.0625 2.19207 11.0625 1.92902C5.75 2.39248 2.15625 6.90188 1.95 11.0668C2.19375 11.0668 2.43125 11.0668 2.675 11.0668C3.29375 11.0668 3.70625 11.4426 3.7125 12C3.7125 12.5637 3.3 12.9395 2.6625 12.9457C2.425 12.9395 2.18125 12.9395 1.925 12.9395Z"
                          fill="var(--text-color)" />
                        <path
                          d="M11.0627 9.23796C11.0627 8.34234 11.0627 7.44047 11.0627 6.54485C11.0627 5.95612 11.4565 5.5365 12.0002 5.5365C12.544 5.5365 12.9377 5.95612 12.9377 6.55111C12.9377 8.15445 12.944 9.75153 12.9315 11.3549C12.9315 11.5428 12.9815 11.6743 13.1127 11.8058C14.1752 12.858 15.2252 13.9165 16.2815 14.9686C16.5065 15.1941 16.6502 15.4572 16.619 15.7828C16.5815 16.1649 16.3815 16.4405 16.019 16.5783C15.6377 16.7223 15.294 16.6346 15.0065 16.3465C14.3377 15.6889 13.6815 15.025 13.019 14.3611C12.494 13.835 11.9752 13.3089 11.444 12.7891C11.1815 12.5323 11.0565 12.2442 11.0627 11.8747C11.069 10.9854 11.0627 10.1085 11.0627 9.23796Z"
                          fill="var(--text-color)" />
                      </svg>
                    </div>
                    <span class="ml-2 text-color">{{content.content | readingTime}}</span>
                  </div>
                  <div class="flex items-center icons_w b_p">
                    <div class="icon-wrap cal_w">
                      <svg width="22" height="24" viewBox="0 0 22 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                          d="M6.41826 5.02326C5.96059 5.02326 5.58105 4.64372 5.58105 4.18605V0.837209C5.58105 0.379535 5.96059 0 6.41826 0C6.87594 0 7.25547 0.379535 7.25547 0.837209V4.18605C7.25547 4.64372 6.87594 5.02326 6.41826 5.02326Z"
                          fill="var(--text-color)" />
                        <path
                          d="M15.3489 5.02326C14.8913 5.02326 14.5117 4.64372 14.5117 4.18605V0.837209C14.5117 0.379535 14.8913 0 15.3489 0C15.8066 0 16.1861 0.379535 16.1861 0.837209V4.18605C16.1861 4.64372 15.8066 5.02326 15.3489 5.02326Z"
                          fill="var(--text-color)" />
                        <path
                          d="M6.97663 14.7908C6.83151 14.7908 6.6864 14.7573 6.55244 14.7015C6.40733 14.6457 6.2957 14.5676 6.18407 14.4671C5.98314 14.255 5.86035 13.9759 5.86035 13.6745C5.86035 13.5294 5.89384 13.3843 5.94965 13.2503C6.00547 13.1164 6.08361 12.9936 6.18407 12.882C6.2957 12.7815 6.40733 12.7034 6.55244 12.6476C6.9543 12.4801 7.45663 12.5694 7.76919 12.882C7.97012 13.0941 8.09291 13.3843 8.09291 13.6745C8.09291 13.7415 8.08175 13.8196 8.07058 13.8978C8.05942 13.9648 8.0371 14.0317 8.00361 14.0987C7.98128 14.1657 7.94779 14.2327 7.90314 14.2996C7.86965 14.3555 7.81384 14.4113 7.76919 14.4671C7.5571 14.668 7.26686 14.7908 6.97663 14.7908Z"
                          fill="var(--text-color)" />
                        <path
                          d="M10.8839 14.7906C10.7387 14.7906 10.5936 14.7572 10.4597 14.7013C10.3146 14.6455 10.2029 14.5674 10.0913 14.4669C9.89037 14.2548 9.76758 13.9758 9.76758 13.6744C9.76758 13.5292 9.80107 13.3841 9.85688 13.2502C9.91269 13.1162 9.99083 12.9934 10.0913 12.8818C10.2029 12.7813 10.3146 12.7032 10.4597 12.6474C10.8615 12.4688 11.3639 12.5692 11.6764 12.8818C11.8773 13.0939 12.0001 13.3841 12.0001 13.6744C12.0001 13.7413 11.989 13.8195 11.9778 13.8976C11.9666 13.9646 11.9443 14.0316 11.9108 14.0986C11.8885 14.1655 11.855 14.2325 11.8104 14.2995C11.7769 14.3553 11.7211 14.4111 11.6764 14.4669C11.4643 14.6679 11.1741 14.7906 10.8839 14.7906Z"
                          fill="var(--text-color)" />
                        <path
                          d="M14.7911 14.7906C14.646 14.7906 14.5009 14.7572 14.3669 14.7013C14.2218 14.6455 14.1102 14.5674 13.9985 14.4669C13.9539 14.4111 13.9092 14.3553 13.8646 14.2995C13.8199 14.2325 13.7864 14.1655 13.7641 14.0986C13.7306 14.0316 13.7083 13.9646 13.6971 13.8976C13.686 13.8195 13.6748 13.7413 13.6748 13.6744C13.6748 13.3841 13.7976 13.0939 13.9985 12.8818C14.1102 12.7813 14.2218 12.7032 14.3669 12.6474C14.7799 12.4688 15.2711 12.5692 15.5836 12.8818C15.7846 13.0939 15.9074 13.3841 15.9074 13.6744C15.9074 13.7413 15.8962 13.8195 15.885 13.8976C15.8739 13.9646 15.8515 14.0316 15.8181 14.0986C15.7957 14.1655 15.7622 14.2325 15.7176 14.2995C15.6841 14.3553 15.6283 14.4111 15.5836 14.4669C15.3715 14.6679 15.0813 14.7906 14.7911 14.7906Z"
                          fill="var(--text-color)" />
                        <path
                          d="M6.97663 18.6976C6.83151 18.6976 6.6864 18.6642 6.55244 18.6084C6.41849 18.5526 6.2957 18.4744 6.18407 18.3739C5.98314 18.1618 5.86035 17.8716 5.86035 17.5813C5.86035 17.4362 5.89384 17.2911 5.94965 17.1572C6.00547 17.012 6.08361 16.8894 6.18407 16.7889C6.5971 16.3759 7.35617 16.3759 7.76919 16.7889C7.97012 17.001 8.09291 17.2911 8.09291 17.5813C8.09291 17.8716 7.97012 18.1618 7.76919 18.3739C7.5571 18.5748 7.26686 18.6976 6.97663 18.6976Z"
                          fill="var(--text-color)" />
                        <path
                          d="M10.8839 18.6976C10.5936 18.6976 10.3034 18.5748 10.0913 18.3739C9.89037 18.1618 9.76758 17.8716 9.76758 17.5813C9.76758 17.4362 9.80107 17.2911 9.85688 17.1572C9.91269 17.012 9.99083 16.8894 10.0913 16.7889C10.5043 16.3759 11.2634 16.3759 11.6764 16.7889C11.7769 16.8894 11.855 17.012 11.9108 17.1572C11.9666 17.2911 12.0001 17.4362 12.0001 17.5813C12.0001 17.8716 11.8773 18.1618 11.6764 18.3739C11.4643 18.5748 11.1741 18.6976 10.8839 18.6976Z"
                          fill="var(--text-color)" />
                        <path
                          d="M14.7911 18.6975C14.5009 18.6975 14.2106 18.5747 13.9985 18.3738C13.8981 18.2733 13.8199 18.1506 13.7641 18.0054C13.7083 17.8715 13.6748 17.7264 13.6748 17.5813C13.6748 17.4361 13.7083 17.291 13.7641 17.1571C13.8199 17.0119 13.8981 16.8892 13.9985 16.7887C14.2553 16.5319 14.646 16.4092 15.0032 16.4873C15.0813 16.4985 15.1483 16.5208 15.2153 16.5543C15.2822 16.5766 15.3492 16.6101 15.4162 16.6547C15.472 16.6882 15.5278 16.744 15.5836 16.7887C15.7846 17.0008 15.9074 17.291 15.9074 17.5813C15.9074 17.8715 15.7846 18.1617 15.5836 18.3738C15.3715 18.5747 15.0813 18.6975 14.7911 18.6975Z"
                          fill="var(--text-color)" />
                        <path
                          d="M20.3716 9.5886H1.39483C0.937152 9.5886 0.557617 9.20907 0.557617 8.75139C0.557617 8.29372 0.937152 7.91418 1.39483 7.91418H20.3716C20.8292 7.91418 21.2088 8.29372 21.2088 8.75139C21.2088 9.20907 20.8292 9.5886 20.3716 9.5886Z"
                          fill="var(--text-color)" />
                        <path
                          d="M15.3488 24H6.4186C2.34419 24 0 21.6558 0 17.5814V8.09304C0 4.01862 2.34419 1.67444 6.4186 1.67444H15.3488C19.4233 1.67444 21.7674 4.01862 21.7674 8.09304V17.5814C21.7674 21.6558 19.4233 24 15.3488 24ZM6.4186 3.34886C3.22605 3.34886 1.67442 4.90049 1.67442 8.09304V17.5814C1.67442 20.774 3.22605 22.3256 6.4186 22.3256H15.3488C18.5414 22.3256 20.093 20.774 20.093 17.5814V8.09304C20.093 4.90049 18.5414 3.34886 15.3488 3.34886H6.4186Z"
                          fill="var(--text-color)" />
                      </svg>
                    </div>
                    <span class="ml-2 text-color">{{content.created_at | date: 'dd/MM/yyyy'}}</span>
                  </div>
                </div>
              </div>
            </div>
            }

            <!-- <div class="buttn_catg" *ngIf="hasMoreContent">
            <button class="load-more-button" (click)="loadMoreContent()" [disabled]="isLoading">
              <span *ngIf="!isLoading">Загрузить еще</span>
              <span *ngIf="isLoading">Загрузка...</span>
            </button>
          </div> -->
          </div>
        </div>

        <!-- Books Tab -->
        <div class="itm_a_wrap" *ngIf="activeTab === 'Книги'">
          @for(item of sort(items); track item.id; let i = $index; let last = $last) {
          <div [ngClass]="{'widen': isOpened[i], 'last': last}" class="article-item relative">
            <div class="vis_part">
              <div (click)="isOpened[i] = !isOpened[i]" class="art_img_"></div>
              <div class="art_img">
                <img [src]="item.image" alt="img">
              </div>
              <div (click)="isOpened[i] = !isOpened[i]" class="flex justify-between">
                <div class="titl_w">
                  <a class="article-title" [routerLink]="['/ru/library/' + item.code]">
                    {{item.title}}
                  </a>
                  <div class="article-category">
                    Автор: {{item.author}} Чтец: {{item.reader}}</div>
                  <div class="flex items-center icons_w b_p">
                    <div class=" flex items-center">
                      <img class="mr-2" src="assets/images/icons/chck.svg" alt="">
                      <span class="ml-1 text-color">
                        {{item.views}} просмотров
                      </span>
                    </div>
                  </div>
                </div>
                <div class="actions_w" (click)="showMenu(i, $event)" [class.show_menu]="show_menu[i]">
                  <button class="flex items-center icons_w lik_hov show_md separatre_"
                    (click)="null;$event.stopPropagation()">
                    <div class="icon-wrap like_w no_h">
                      <svg class="emty_l" width="21" height="22" viewBox="0 0 21 22" fill="none"
                        xmlns="http://www.w3.org/2000/svg">
                        <path
                          d="M19.4948 12.1068C17.2112 10.6027 14.8396 10.3315 12.4366 11.589C10.0398 12.8404 8.93264 14.9055 8.90748 17.5685C8.81941 17.5685 8.74392 17.5685 8.66843 17.5685C7.42916 17.5685 6.18989 17.5685 4.95061 17.5685C4.36558 17.5685 4.12024 17.3342 4.12024 16.7548C4.12024 11.4411 4.12024 6.12739 4.12024 0.813697C4.11395 0.234246 4.35299 0 4.94432 0C8.97039 0 12.9964 0 17.0225 0C18.4945 0 19.4948 0.9863 19.4948 2.4226C19.4948 5.57259 19.4948 8.72875 19.4948 11.8787C19.4948 11.9466 19.4948 12.0144 19.4948 12.1068ZM11.8201 6.20136C12.9021 6.20136 13.9841 6.20136 15.0661 6.20136C15.5316 6.20136 15.8399 5.94862 15.8461 5.5541C15.8524 5.16574 15.5442 4.87602 15.0913 4.87602C12.9021 4.86985 10.7129 4.87602 8.52374 4.87602C8.39164 4.87602 8.25324 4.90684 8.14001 4.95616C7.85693 5.08561 7.71224 5.39999 7.78773 5.68972C7.86322 5.99177 8.12114 6.20136 8.45455 6.20136C9.5743 6.20136 10.694 6.20136 11.8201 6.20136ZM11.8201 8.96916C12.3359 8.96916 12.8518 8.96916 13.3739 8.96916C13.808 8.96916 14.1225 8.69177 14.1225 8.30341C14.1225 7.92122 13.8142 7.63766 13.3739 7.63766C12.3296 7.63149 11.2917 7.63149 10.2474 7.63766C9.80076 7.63766 9.51139 7.91506 9.51139 8.30341C9.51139 8.69177 9.81334 8.963 10.2537 8.963C10.7695 8.97533 11.2917 8.96916 11.8201 8.96916Z"
                          fill="var(--font-color1)" />
                        <path
                          d="M3.04471 0.0124731C3.02584 0.406993 2.98809 0.789184 2.98809 1.17138C2.9818 6.27548 2.9818 11.3857 2.98809 16.4898C2.98809 16.8412 3.03213 17.1926 3.051 17.5563C2.99438 17.5624 2.89373 17.5748 2.79308 17.5809C1.90609 17.6549 1.39025 18.407 1.36509 19.1837C1.33992 19.9172 1.98787 20.5707 2.7805 20.657C2.90631 20.6693 3.03213 20.6755 3.15794 20.6755C4.98854 20.6755 6.81914 20.6755 8.64345 20.6755C8.73782 20.6755 8.83218 20.6755 8.87621 20.6755C9.05235 21.1316 9.21591 21.557 9.37947 21.9885C9.33543 21.9885 9.26624 21.9946 9.19704 21.9946C7.13368 21.9946 5.07032 22.0008 3.00068 21.9946C1.51606 21.9885 0.333408 21.0576 0.0566165 19.6645C-0.00629073 19.3563 0.00629072 19.0357 0.00629072 18.7213C0.00629072 13.4816 0.00629072 8.24808 0 3.00836C0 1.86179 0.478095 0.974115 1.48461 0.376171C1.98158 0.0987743 2.51629 -0.0306776 3.04471 0.0124731Z"
                          fill="var(--font-color1)" />
                        <path
                          d="M19.6212 17.1801C19.6149 14.9363 17.5956 13.1178 15.2303 13.2719C12.6826 13.4383 11.3678 15.6452 11.4433 17.1739C11.8711 17.1739 12.3051 17.1739 12.7329 17.1739C13.7205 17.1801 14.4062 17.8397 14.4251 18.8013C14.4314 19.3253 14.4377 19.8493 14.4251 20.3733C14.4062 21.3164 13.7331 21.976 12.7706 21.9945C12.3554 22.0007 11.9403 22.0068 11.5251 21.9822C10.8016 21.939 10.1034 21.3287 10.0908 20.626C10.0593 19.2513 9.99642 17.8644 10.1411 16.502C10.399 14.1041 12.4813 12.2055 14.9158 11.9589C17.7843 11.6753 20.3132 13.4877 20.8857 16.2493C20.9611 16.613 20.9863 16.9952 20.9926 17.3712C21.0052 18.3328 20.9989 19.2945 20.9989 20.2561C20.9989 21.3226 20.3195 21.9883 19.2375 21.9945C18.8852 21.9945 18.5329 22.0007 18.1806 21.9883C17.3125 21.9513 16.6457 21.2794 16.6268 20.4349C16.6143 19.8678 16.6143 19.2945 16.6268 18.7274C16.6457 17.8644 17.344 17.1863 18.2247 17.1739C18.6776 17.1678 19.1431 17.1801 19.6212 17.1801Z"
                          fill="var(--font-color1)" />
                      </svg>
                      <svg class="emty_l_hover" width="21" height="22" viewBox="0 0 21 22" fill="none"
                        xmlns="http://www.w3.org/2000/svg">
                        <path
                          d="M19.4948 12.1068C17.2112 10.6027 14.8396 10.3315 12.4366 11.589C10.0398 12.8404 8.93264 14.9055 8.90748 17.5685C8.81941 17.5685 8.74392 17.5685 8.66843 17.5685C7.42916 17.5685 6.18989 17.5685 4.95061 17.5685C4.36558 17.5685 4.12024 17.3342 4.12024 16.7548C4.12024 11.4411 4.12024 6.12739 4.12024 0.813697C4.11395 0.234246 4.35299 0 4.94432 0C8.97039 0 12.9964 0 17.0225 0C18.4945 0 19.4948 0.9863 19.4948 2.4226C19.4948 5.57259 19.4948 8.72875 19.4948 11.8787C19.4948 11.9466 19.4948 12.0144 19.4948 12.1068ZM11.8201 6.20136C12.9021 6.20136 13.9841 6.20136 15.0661 6.20136C15.5316 6.20136 15.8399 5.94862 15.8461 5.5541C15.8524 5.16574 15.5442 4.87602 15.0913 4.87602C12.9021 4.86985 10.7129 4.87602 8.52374 4.87602C8.39164 4.87602 8.25324 4.90684 8.14001 4.95616C7.85693 5.08561 7.71224 5.39999 7.78773 5.68972C7.86322 5.99177 8.12114 6.20136 8.45455 6.20136C9.5743 6.20136 10.694 6.20136 11.8201 6.20136ZM11.8201 8.96916C12.3359 8.96916 12.8518 8.96916 13.3739 8.96916C13.808 8.96916 14.1225 8.69177 14.1225 8.30341C14.1225 7.92122 13.8142 7.63766 13.3739 7.63766C12.3296 7.63149 11.2917 7.63149 10.2474 7.63766C9.80076 7.63766 9.51139 7.91506 9.51139 8.30341C9.51139 8.69177 9.81334 8.963 10.2537 8.963C10.7695 8.97533 11.2917 8.96916 11.8201 8.96916Z"
                          fill="var(--text-color)" />
                        <path
                          d="M3.04471 0.0124731C3.02584 0.406993 2.98809 0.789184 2.98809 1.17138C2.9818 6.27548 2.9818 11.3857 2.98809 16.4898C2.98809 16.8412 3.03213 17.1926 3.051 17.5563C2.99438 17.5624 2.89373 17.5748 2.79308 17.5809C1.90609 17.6549 1.39025 18.407 1.36509 19.1837C1.33992 19.9172 1.98787 20.5707 2.7805 20.657C2.90631 20.6693 3.03213 20.6755 3.15794 20.6755C4.98854 20.6755 6.81914 20.6755 8.64345 20.6755C8.73782 20.6755 8.83218 20.6755 8.87621 20.6755C9.05235 21.1316 9.21591 21.557 9.37947 21.9885C9.33543 21.9885 9.26624 21.9946 9.19704 21.9946C7.13368 21.9946 5.07032 22.0008 3.00068 21.9946C1.51606 21.9885 0.333408 21.0576 0.0566165 19.6645C-0.00629073 19.3563 0.00629072 19.0357 0.00629072 18.7213C0.00629072 13.4816 0.00629072 8.24808 0 3.00836C0 1.86179 0.478095 0.974115 1.48461 0.376171C1.98158 0.0987743 2.51629 -0.0306776 3.04471 0.0124731Z"
                          fill="var(--text-color)" />
                        <path
                          d="M19.6212 17.1801C19.6149 14.9363 17.5956 13.1178 15.2303 13.2719C12.6826 13.4383 11.3678 15.6452 11.4433 17.1739C11.8711 17.1739 12.3051 17.1739 12.7329 17.1739C13.7205 17.1801 14.4062 17.8397 14.4251 18.8013C14.4314 19.3253 14.4377 19.8493 14.4251 20.3733C14.4062 21.3164 13.7331 21.976 12.7706 21.9945C12.3554 22.0007 11.9403 22.0068 11.5251 21.9822C10.8016 21.939 10.1034 21.3287 10.0908 20.626C10.0593 19.2513 9.99642 17.8644 10.1411 16.502C10.399 14.1041 12.4813 12.2055 14.9158 11.9589C17.7843 11.6753 20.3132 13.4877 20.8857 16.2493C20.9611 16.613 20.9863 16.9952 20.9926 17.3712C21.0052 18.3328 20.9989 19.2945 20.9989 20.2561C20.9989 21.3226 20.3195 21.9883 19.2375 21.9945C18.8852 21.9945 18.5329 22.0007 18.1806 21.9883C17.3125 21.9513 16.6457 21.2794 16.6268 20.4349C16.6143 19.8678 16.6143 19.2945 16.6268 18.7274C16.6457 17.8644 17.344 17.1863 18.2247 17.1739C18.6776 17.1678 19.1431 17.1801 19.6212 17.1801Z"
                          fill="var(--text-color)" />
                      </svg>
                    </div>
                    <p class="show_p_md">
                      слушать
                    </p>
                    <div class="on_hov shrt">
                      слушать
                    </div>
                  </button>
                  <div class="flex items-center cursor-pointer icons_w show_md">
                    <div class="icon-wrap star_w no_h">
                      <svg width="7" height="22" viewBox="0 0 7 22" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="3.27778" cy="3.27778" r="2.27778" fill="#DEA53D" stroke="#DEA53D" />
                        <circle cx="3.27778" cy="11.2499" r="2.27778" fill="#DEA53D" stroke="#DEA53D" />
                        <circle cx="3.27778" cy="19.2221" r="2.27778" fill="#DEA53D" stroke="#DEA53D" />
                      </svg>
                    </div>
                  </div>
                  <div class="md_chg">
                    <button class="flex items-center icons_w lik_hov d__none" (click)="null">
                      <div class="icon-wrap like_w">
                        <svg class="emty_l" width="21" height="22" viewBox="0 0 21 22" fill="none"
                          xmlns="http://www.w3.org/2000/svg">
                          <path
                            d="M19.4948 12.1068C17.2112 10.6027 14.8396 10.3315 12.4366 11.589C10.0398 12.8404 8.93264 14.9055 8.90748 17.5685C8.81941 17.5685 8.74392 17.5685 8.66843 17.5685C7.42916 17.5685 6.18989 17.5685 4.95061 17.5685C4.36558 17.5685 4.12024 17.3342 4.12024 16.7548C4.12024 11.4411 4.12024 6.12739 4.12024 0.813697C4.11395 0.234246 4.35299 0 4.94432 0C8.97039 0 12.9964 0 17.0225 0C18.4945 0 19.4948 0.9863 19.4948 2.4226C19.4948 5.57259 19.4948 8.72875 19.4948 11.8787C19.4948 11.9466 19.4948 12.0144 19.4948 12.1068ZM11.8201 6.20136C12.9021 6.20136 13.9841 6.20136 15.0661 6.20136C15.5316 6.20136 15.8399 5.94862 15.8461 5.5541C15.8524 5.16574 15.5442 4.87602 15.0913 4.87602C12.9021 4.86985 10.7129 4.87602 8.52374 4.87602C8.39164 4.87602 8.25324 4.90684 8.14001 4.95616C7.85693 5.08561 7.71224 5.39999 7.78773 5.68972C7.86322 5.99177 8.12114 6.20136 8.45455 6.20136C9.5743 6.20136 10.694 6.20136 11.8201 6.20136ZM11.8201 8.96916C12.3359 8.96916 12.8518 8.96916 13.3739 8.96916C13.808 8.96916 14.1225 8.69177 14.1225 8.30341C14.1225 7.92122 13.8142 7.63766 13.3739 7.63766C12.3296 7.63149 11.2917 7.63149 10.2474 7.63766C9.80076 7.63766 9.51139 7.91506 9.51139 8.30341C9.51139 8.69177 9.81334 8.963 10.2537 8.963C10.7695 8.97533 11.2917 8.96916 11.8201 8.96916Z"
                            fill="var(--font-color1)" />
                          <path
                            d="M3.04471 0.0124731C3.02584 0.406993 2.98809 0.789184 2.98809 1.17138C2.9818 6.27548 2.9818 11.3857 2.98809 16.4898C2.98809 16.8412 3.03213 17.1926 3.051 17.5563C2.99438 17.5624 2.89373 17.5748 2.79308 17.5809C1.90609 17.6549 1.39025 18.407 1.36509 19.1837C1.33992 19.9172 1.98787 20.5707 2.7805 20.657C2.90631 20.6693 3.03213 20.6755 3.15794 20.6755C4.98854 20.6755 6.81914 20.6755 8.64345 20.6755C8.73782 20.6755 8.83218 20.6755 8.87621 20.6755C9.05235 21.1316 9.21591 21.557 9.37947 21.9885C9.33543 21.9885 9.26624 21.9946 9.19704 21.9946C7.13368 21.9946 5.07032 22.0008 3.00068 21.9946C1.51606 21.9885 0.333408 21.0576 0.0566165 19.6645C-0.00629073 19.3563 0.00629072 19.0357 0.00629072 18.7213C0.00629072 13.4816 0.00629072 8.24808 0 3.00836C0 1.86179 0.478095 0.974115 1.48461 0.376171C1.98158 0.0987743 2.51629 -0.0306776 3.04471 0.0124731Z"
                            fill="var(--font-color1)" />
                          <path
                            d="M19.6212 17.1801C19.6149 14.9363 17.5956 13.1178 15.2303 13.2719C12.6826 13.4383 11.3678 15.6452 11.4433 17.1739C11.8711 17.1739 12.3051 17.1739 12.7329 17.1739C13.7205 17.1801 14.4062 17.8397 14.4251 18.8013C14.4314 19.3253 14.4377 19.8493 14.4251 20.3733C14.4062 21.3164 13.7331 21.976 12.7706 21.9945C12.3554 22.0007 11.9403 22.0068 11.5251 21.9822C10.8016 21.939 10.1034 21.3287 10.0908 20.626C10.0593 19.2513 9.99642 17.8644 10.1411 16.502C10.399 14.1041 12.4813 12.2055 14.9158 11.9589C17.7843 11.6753 20.3132 13.4877 20.8857 16.2493C20.9611 16.613 20.9863 16.9952 20.9926 17.3712C21.0052 18.3328 20.9989 19.2945 20.9989 20.2561C20.9989 21.3226 20.3195 21.9883 19.2375 21.9945C18.8852 21.9945 18.5329 22.0007 18.1806 21.9883C17.3125 21.9513 16.6457 21.2794 16.6268 20.4349C16.6143 19.8678 16.6143 19.2945 16.6268 18.7274C16.6457 17.8644 17.344 17.1863 18.2247 17.1739C18.6776 17.1678 19.1431 17.1801 19.6212 17.1801Z"
                            fill="var(--font-color1)" />
                        </svg>
                        <svg class="emty_l_hover" width="21" height="22" viewBox="0 0 21 22" fill="none"
                          xmlns="http://www.w3.org/2000/svg">
                          <path
                            d="M19.4948 12.1068C17.2112 10.6027 14.8396 10.3315 12.4366 11.589C10.0398 12.8404 8.93264 14.9055 8.90748 17.5685C8.81941 17.5685 8.74392 17.5685 8.66843 17.5685C7.42916 17.5685 6.18989 17.5685 4.95061 17.5685C4.36558 17.5685 4.12024 17.3342 4.12024 16.7548C4.12024 11.4411 4.12024 6.12739 4.12024 0.813697C4.11395 0.234246 4.35299 0 4.94432 0C8.97039 0 12.9964 0 17.0225 0C18.4945 0 19.4948 0.9863 19.4948 2.4226C19.4948 5.57259 19.4948 8.72875 19.4948 11.8787C19.4948 11.9466 19.4948 12.0144 19.4948 12.1068ZM11.8201 6.20136C12.9021 6.20136 13.9841 6.20136 15.0661 6.20136C15.5316 6.20136 15.8399 5.94862 15.8461 5.5541C15.8524 5.16574 15.5442 4.87602 15.0913 4.87602C12.9021 4.86985 10.7129 4.87602 8.52374 4.87602C8.39164 4.87602 8.25324 4.90684 8.14001 4.95616C7.85693 5.08561 7.71224 5.39999 7.78773 5.68972C7.86322 5.99177 8.12114 6.20136 8.45455 6.20136C9.5743 6.20136 10.694 6.20136 11.8201 6.20136ZM11.8201 8.96916C12.3359 8.96916 12.8518 8.96916 13.3739 8.96916C13.808 8.96916 14.1225 8.69177 14.1225 8.30341C14.1225 7.92122 13.8142 7.63766 13.3739 7.63766C12.3296 7.63149 11.2917 7.63149 10.2474 7.63766C9.80076 7.63766 9.51139 7.91506 9.51139 8.30341C9.51139 8.69177 9.81334 8.963 10.2537 8.963C10.7695 8.97533 11.2917 8.96916 11.8201 8.96916Z"
                            fill="var(--text-color)" />
                          <path
                            d="M3.04471 0.0124731C3.02584 0.406993 2.98809 0.789184 2.98809 1.17138C2.9818 6.27548 2.9818 11.3857 2.98809 16.4898C2.98809 16.8412 3.03213 17.1926 3.051 17.5563C2.99438 17.5624 2.89373 17.5748 2.79308 17.5809C1.90609 17.6549 1.39025 18.407 1.36509 19.1837C1.33992 19.9172 1.98787 20.5707 2.7805 20.657C2.90631 20.6693 3.03213 20.6755 3.15794 20.6755C4.98854 20.6755 6.81914 20.6755 8.64345 20.6755C8.73782 20.6755 8.83218 20.6755 8.87621 20.6755C9.05235 21.1316 9.21591 21.557 9.37947 21.9885C9.33543 21.9885 9.26624 21.9946 9.19704 21.9946C7.13368 21.9946 5.07032 22.0008 3.00068 21.9946C1.51606 21.9885 0.333408 21.0576 0.0566165 19.6645C-0.00629073 19.3563 0.00629072 19.0357 0.00629072 18.7213C0.00629072 13.4816 0.00629072 8.24808 0 3.00836C0 1.86179 0.478095 0.974115 1.48461 0.376171C1.98158 0.0987743 2.51629 -0.0306776 3.04471 0.0124731Z"
                            fill="var(--text-color)" />
                          <path
                            d="M19.6212 17.1801C19.6149 14.9363 17.5956 13.1178 15.2303 13.2719C12.6826 13.4383 11.3678 15.6452 11.4433 17.1739C11.8711 17.1739 12.3051 17.1739 12.7329 17.1739C13.7205 17.1801 14.4062 17.8397 14.4251 18.8013C14.4314 19.3253 14.4377 19.8493 14.4251 20.3733C14.4062 21.3164 13.7331 21.976 12.7706 21.9945C12.3554 22.0007 11.9403 22.0068 11.5251 21.9822C10.8016 21.939 10.1034 21.3287 10.0908 20.626C10.0593 19.2513 9.99642 17.8644 10.1411 16.502C10.399 14.1041 12.4813 12.2055 14.9158 11.9589C17.7843 11.6753 20.3132 13.4877 20.8857 16.2493C20.9611 16.613 20.9863 16.9952 20.9926 17.3712C21.0052 18.3328 20.9989 19.2945 20.9989 20.2561C20.9989 21.3226 20.3195 21.9883 19.2375 21.9945C18.8852 21.9945 18.5329 22.0007 18.1806 21.9883C17.3125 21.9513 16.6457 21.2794 16.6268 20.4349C16.6143 19.8678 16.6143 19.2945 16.6268 18.7274C16.6457 17.8644 17.344 17.1863 18.2247 17.1739C18.6776 17.1678 19.1431 17.1801 19.6212 17.1801Z"
                            fill="var(--text-color)" />
                        </svg>
                      </div>
                      <p class="show_p_md">
                        слушать
                      </p>
                      <div class="on_hov shrt">
                        слушать
                      </div>
                    </button>
                    <div class="flex items-center cursor-pointer icons_w fav_hov"
                      [ngClass]="{'in-favourites': item.inFavourites}" (click)="favorites(item)">
                      <div class="icon-wrap star_w">
                        <svg class="emty_f" width="25" height="24" viewBox="0 0 25 24" fill="none"
                          xmlns="http://www.w3.org/2000/svg">
                          <path
                            d="M5.96868 23.9618C5.94253 23.9618 5.91638 23.9618 5.89022 23.9618C5.36715 23.9356 4.89638 23.6999 4.53023 23.3333C4.16408 22.9143 3.98101 22.3906 4.03332 21.8145C4.05947 21.5789 4.11178 21.317 4.19024 21.0552C4.71331 19.4055 5.41946 17.2844 6.1256 15.1372C4.42562 13.9065 2.72564 12.6758 1.02565 11.4189C0.816424 11.2618 0.162584 10.7904 0.0318159 9.95249C-0.0727985 9.35022 0.0841231 8.77414 0.450273 8.35517C0.842577 7.88383 1.44411 7.62197 2.15026 7.62197C2.90871 7.62197 3.66717 7.62197 4.42562 7.62197H8.55789C8.68866 7.25537 8.79327 6.88878 8.92404 6.52218C9.08096 6.05084 9.21173 5.60568 9.36865 5.13435C9.7348 4.00837 10.0748 2.8562 10.4671 1.73022C10.5979 1.31125 10.8333 0.918472 11.1209 0.604245C11.4871 0.211462 12.0363 -0.0242083 12.5855 0.00197721C13.1348 0.0281628 13.6578 0.290018 14.024 0.735173C14.2332 1.02321 14.4163 1.33744 14.5471 1.70404C15.044 3.24899 15.5409 4.79393 16.0378 6.33888L16.4563 7.62197H17.6593C19.3332 7.62197 21.007 7.62197 22.6808 7.62197C23.047 7.62197 23.387 7.67434 23.7008 7.77909C24.2762 7.98857 24.7208 8.43372 24.9039 9.00981C25.087 9.58589 25.0085 10.1882 24.6685 10.7119C24.4854 10.9999 24.25 11.2356 23.9624 11.4451C22.2624 12.6758 20.5624 13.9327 18.8886 15.1372C19.5424 17.1273 20.1962 19.0912 20.8239 21.0813C21.0593 21.7884 21.0332 22.3906 20.7455 22.9405C20.327 23.7785 19.3593 24.1974 18.4701 23.9094C18.1301 23.8046 17.8163 23.6475 17.5286 23.438C16.1424 22.443 14.7563 21.4218 13.344 20.4005L12.5071 19.7983L11.5132 20.5314C10.1794 21.5003 8.84558 22.4954 7.51174 23.4642C6.96252 23.7785 6.4656 23.9618 5.96868 23.9618ZM3.45794 9.14073C3.01333 9.14073 2.59487 9.14073 2.15026 9.14073C1.91487 9.14073 1.7318 9.21929 1.65334 9.32403C1.57488 9.40259 1.54872 9.53352 1.57488 9.66445C1.60103 9.76919 1.65334 9.90012 1.94103 10.1096C3.71947 11.3927 5.47176 12.702 7.25021 13.9851C7.69482 14.3255 7.85174 14.7968 7.69482 15.3205C6.96252 17.5463 6.23022 19.7983 5.68099 21.5003C5.62869 21.6312 5.60253 21.7884 5.60253 21.8931C5.60253 22.0502 5.62869 22.155 5.70715 22.2597C5.75945 22.3383 5.86407 22.3644 5.94253 22.3644C6.0733 22.3644 6.23022 22.3383 6.54406 22.1026C7.87789 21.1337 9.21173 20.1387 10.5456 19.1698L12.4809 17.7558L14.2594 19.0389C15.6455 20.0601 17.0317 21.0552 18.4178 22.0764C18.5747 22.1811 18.7316 22.2597 18.9147 22.3121C19.0716 22.3644 19.2286 22.2859 19.307 22.1288C19.3593 22.024 19.4116 21.8407 19.2809 21.4741C18.6009 19.4055 17.9209 17.3368 17.2409 15.2682C17.0578 14.7183 17.2147 14.2731 17.6593 13.9327C19.4116 12.6758 21.1901 11.3665 22.9424 10.0834C23.0731 9.97867 23.1777 9.87393 23.2824 9.743C23.3608 9.63826 23.387 9.50733 23.3347 9.40259C23.3085 9.29785 23.2039 9.21929 23.0993 9.16692C22.9685 9.11455 22.7854 9.08836 22.6024 9.08836C20.9285 9.08836 19.2547 9.08836 17.5809 9.08836H15.2794L15.0963 8.59084C15.0701 8.51228 15.044 8.45991 15.0178 8.40754L14.4948 6.78404C13.9978 5.23909 13.5009 3.69414 13.004 2.14919C12.9517 1.96589 12.8471 1.78259 12.7425 1.65167C12.664 1.54692 12.5332 1.52074 12.4809 1.52074C12.4286 1.52074 12.324 1.52074 12.2455 1.62548C12.1148 1.75641 12.0102 1.96589 11.9317 2.17538C11.5656 3.30136 11.1994 4.45352 10.8333 5.55331C10.6763 6.02465 10.5456 6.46981 10.3886 6.94115C10.2579 7.30775 10.1533 7.70053 10.0225 8.06713L9.68249 9.14073H4.42562C4.11178 9.14073 3.77178 9.14073 3.45794 9.14073Z"
                            fill="var(--font-color1)" />
                        </svg>
                        <svg class="emty_f_hover" width="25" height="24" viewBox="0 0 25 24" fill="none"
                          xmlns="http://www.w3.org/2000/svg">
                          <path
                            d="M5.96868 23.9618C5.94253 23.9618 5.91638 23.9618 5.89022 23.9618C5.36715 23.9356 4.89638 23.6999 4.53023 23.3333C4.16408 22.9143 3.98101 22.3906 4.03332 21.8145C4.05947 21.5789 4.11178 21.317 4.19024 21.0552C4.71331 19.4055 5.41946 17.2844 6.1256 15.1372C4.42562 13.9065 2.72564 12.6758 1.02565 11.4189C0.816424 11.2618 0.162584 10.7904 0.0318159 9.95249C-0.0727985 9.35022 0.0841231 8.77414 0.450273 8.35517C0.842577 7.88383 1.44411 7.62197 2.15026 7.62197C2.90871 7.62197 3.66717 7.62197 4.42562 7.62197H8.55789C8.68866 7.25537 8.79327 6.88878 8.92404 6.52218C9.08096 6.05084 9.21173 5.60568 9.36865 5.13435C9.7348 4.00837 10.0748 2.8562 10.4671 1.73022C10.5979 1.31125 10.8333 0.918472 11.1209 0.604245C11.4871 0.211462 12.0363 -0.0242083 12.5855 0.00197721C13.1348 0.0281628 13.6578 0.290018 14.024 0.735173C14.2332 1.02321 14.4163 1.33744 14.5471 1.70404C15.044 3.24899 15.5409 4.79393 16.0378 6.33888L16.4563 7.62197H17.6593C19.3332 7.62197 21.007 7.62197 22.6808 7.62197C23.047 7.62197 23.387 7.67434 23.7008 7.77909C24.2762 7.98857 24.7208 8.43372 24.9039 9.00981C25.087 9.58589 25.0085 10.1882 24.6685 10.7119C24.4854 10.9999 24.25 11.2356 23.9624 11.4451C22.2624 12.6758 20.5624 13.9327 18.8886 15.1372C19.5424 17.1273 20.1962 19.0912 20.8239 21.0813C21.0593 21.7884 21.0332 22.3906 20.7455 22.9405C20.327 23.7785 19.3593 24.1974 18.4701 23.9094C18.1301 23.8046 17.8163 23.6475 17.5286 23.438C16.1424 22.443 14.7563 21.4218 13.344 20.4005L12.5071 19.7983L11.5132 20.5314C10.1794 21.5003 8.84558 22.4954 7.51174 23.4642C6.96252 23.7785 6.4656 23.9618 5.96868 23.9618ZM3.45794 9.14073C3.01333 9.14073 2.59487 9.14073 2.15026 9.14073C1.91487 9.14073 1.7318 9.21929 1.65334 9.32403C1.57488 9.40259 1.54872 9.53352 1.57488 9.66445C1.60103 9.76919 1.65334 9.90012 1.94103 10.1096C3.71947 11.3927 5.47176 12.702 7.25021 13.9851C7.69482 14.3255 7.85174 14.7968 7.69482 15.3205C6.96252 17.5463 6.23022 19.7983 5.68099 21.5003C5.62869 21.6312 5.60253 21.7884 5.60253 21.8931C5.60253 22.0502 5.62869 22.155 5.70715 22.2597C5.75945 22.3383 5.86407 22.3644 5.94253 22.3644C6.0733 22.3644 6.23022 22.3383 6.54406 22.1026C7.87789 21.1337 9.21173 20.1387 10.5456 19.1698L12.4809 17.7558L14.2594 19.0389C15.6455 20.0601 17.0317 21.0552 18.4178 22.0764C18.5747 22.1811 18.7316 22.2597 18.9147 22.3121C19.0716 22.3644 19.2286 22.2859 19.307 22.1288C19.3593 22.024 19.4116 21.8407 19.2809 21.4741C18.6009 19.4055 17.9209 17.3368 17.2409 15.2682C17.0578 14.7183 17.2147 14.2731 17.6593 13.9327C19.4116 12.6758 21.1901 11.3665 22.9424 10.0834C23.0731 9.97867 23.1777 9.87393 23.2824 9.743C23.3608 9.63826 23.387 9.50733 23.3347 9.40259C23.3085 9.29785 23.2039 9.21929 23.0993 9.16692C22.9685 9.11455 22.7854 9.08836 22.6024 9.08836C20.9285 9.08836 19.2547 9.08836 17.5809 9.08836H15.2794L15.0963 8.59084C15.0701 8.51228 15.044 8.45991 15.0178 8.40754L14.4948 6.78404C13.9978 5.23909 13.5009 3.69414 13.004 2.14919C12.9517 1.96589 12.8471 1.78259 12.7425 1.65167C12.664 1.54692 12.5332 1.52074 12.4809 1.52074C12.4286 1.52074 12.324 1.52074 12.2455 1.62548C12.1148 1.75641 12.0102 1.96589 11.9317 2.17538C11.5656 3.30136 11.1994 4.45352 10.8333 5.55331C10.6763 6.02465 10.5456 6.46981 10.3886 6.94115C10.2579 7.30775 10.1533 7.70053 10.0225 8.06713L9.68249 9.14073H4.42562C4.11178 9.14073 3.77178 9.14073 3.45794 9.14073Z"
                            fill="var(--text-color)" />
                        </svg>
                      </div>
                      <p class="show_p_md">
                        добавить в избранное
                      </p>
                      <div class="on_hov">
                        добавить в избранное
                      </div>
                    </div>
                    <div class="flex items-center cursor-pointer icons_w shr_hov" *ngIf="profileService.profile"
                      (click)="share(item)">
                      <div class="icon-wrap share_w">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path
                            d="M15.9238 7.01009C13.5066 8.21941 11.0965 9.42157 8.66504 10.638C8.9368 11.5325 8.94395 12.427 8.66504 13.3357C11.0822 14.5451 13.4923 15.7544 15.8737 16.9422C16.2456 16.5988 16.5745 16.2267 16.9607 15.9476C19.6353 13.9941 23.4542 15.6113 23.9334 18.8957C24.3481 21.7509 22.0168 24.2268 19.1347 23.9835C17.0894 23.8117 15.3731 22.1516 15.1514 20.1051C15.1085 19.6829 15.1156 19.2464 15.1729 18.8313C15.2086 18.588 15.1442 18.5093 14.9512 18.4163C12.8772 17.3859 10.8105 16.3483 8.73656 15.3107C8.50056 15.1891 8.25741 15.0817 8.02141 14.9458C7.87838 14.8671 7.79972 14.8885 7.6853 15.0102C6.76276 15.9834 5.63283 16.4556 4.28836 16.4413C2.21445 16.4127 0.326467 14.7454 0.0475605 12.6846C-0.288557 10.2588 1.18464 8.12638 3.58752 7.63979C5.16084 7.32494 6.52676 7.79722 7.65669 8.94213C7.80687 9.09241 7.89984 9.10672 8.07862 9.01369C10.3742 7.85447 12.677 6.70955 14.9798 5.56464C15.1585 5.47877 15.2158 5.40005 15.1729 5.18538C14.7295 2.75959 16.4458 0.39105 18.8773 0.047575C21.4232 -0.317367 23.7117 1.45725 23.9763 3.99753C24.2266 6.37323 22.4673 8.57004 20.093 8.8348C18.4196 9.028 17.0751 8.41977 16.0239 7.12458C15.9881 7.09596 15.9595 7.06018 15.9238 7.01009ZM7.14179 11.9976C7.14179 10.5021 5.91174 9.26414 4.42424 9.2713C2.94389 9.27845 1.721 10.4949 1.70669 11.9762C1.69239 13.4574 2.92244 14.7025 4.41709 14.7168C5.90459 14.7311 7.14179 13.5003 7.14179 11.9976ZM16.832 19.5541C16.8248 21.0496 18.0549 22.2876 19.5424 22.2804C21.0227 22.2804 22.2671 21.0353 22.2671 19.5541C22.2599 18.0728 21.0513 16.8635 19.5567 16.8492C18.062 16.8277 16.8391 18.0442 16.832 19.5541ZM19.5424 7.14605C21.037 7.14605 22.2671 5.91527 22.2671 4.42688C22.2599 2.93849 21.0156 1.70055 19.5352 1.7077C18.0477 1.71486 16.8463 2.93133 16.832 4.41972C16.8248 5.92242 18.0406 7.14605 19.5424 7.14605Z"
                            fill="var(--font-color1)" />
                        </svg>
                      </div>
                      <p class="show_p_md">
                        поделиться
                      </p>
                      <div class="on_hov">
                        поделиться
                      </div>
                    </div>
                    <button class="flex items-center icons_w lik_hov" [ngClass]="{'is-liked': item.liked}"
                      (click)="likeLibrary(item)">
                      <div class="icon-wrap like_w">
                        <svg class="emty_l" width="24" height="22" viewBox="0 0 24 22" fill="none"
                          xmlns="http://www.w3.org/2000/svg">
                          <path
                            d="M11.7534 2.35249C11.576 2.19912 11.4123 2.05272 11.2486 1.89935C10.048 0.749078 8.90875 0.163484 7.67404 0.0589139C7.44892 0.0379998 7.22381 0.0310285 7.00552 0.0310285C4.87718 0.0310285 3.11038 0.909419 1.72559 2.65923C0.286236 4.47875 -0.252671 6.65381 0.108874 9.12864C0.402203 11.1294 1.2549 12.7816 2.63969 14.0365C4.74757 15.9397 6.91684 17.8428 9.01789 19.6763C9.79555 20.3595 10.5732 21.0357 11.3509 21.7189C11.4668 21.8235 11.5896 21.9002 11.7056 21.942C12.0399 22.0675 12.3468 21.9908 12.6674 21.698C13.8612 20.6453 15.055 19.5926 16.2556 18.54C17.0742 17.815 17.8996 17.0969 18.7182 16.3719C19.0047 16.1209 19.2981 15.8769 19.5777 15.6329C20.2258 15.0822 20.8943 14.5175 21.4946 13.8971C23.6229 11.6802 24.4006 9.03105 23.8071 6.03336C23.3023 3.49579 21.9517 1.69718 19.796 0.679364C17.7291 -0.296625 15.7713 -0.21994 13.9908 0.902448C13.3223 1.32073 12.7016 1.87844 12.1422 2.55466L12.074 2.63831L11.9989 2.5686C11.9171 2.49192 11.8352 2.4222 11.7534 2.35249ZM12.7561 4.47875C12.8175 4.38812 12.8789 4.2975 12.9403 4.20687C13.1586 3.87922 13.3837 3.54459 13.6566 3.25877C15.13 1.69021 17.4426 1.39044 19.4072 2.52677C20.8602 3.36334 21.7538 4.69486 22.0744 6.47953C22.5315 9.0659 21.7675 11.2689 19.7892 13.0187C18.0224 14.5802 16.2215 16.1697 14.4752 17.6964C13.7316 18.3517 12.9881 19.0001 12.2513 19.6554C12.2172 19.6902 12.1763 19.7181 12.1354 19.753L11.9989 19.8645L8.01512 16.3579C7.59218 15.9815 7.15559 15.605 6.73265 15.2425C5.79809 14.4338 4.82943 13.5973 3.90851 12.7328C2.5783 11.485 1.8211 9.77698 1.78699 7.92957C1.75288 6.17976 2.37365 4.51361 3.49921 3.36334C5.02043 1.80872 7.43528 1.39741 9.23618 2.38037C9.94563 2.77077 10.5459 3.35636 11.0576 4.17201C11.0848 4.21384 11.1121 4.25567 11.1326 4.2975C11.1667 4.36024 11.2008 4.41601 11.2417 4.47178C11.4191 4.73669 11.692 4.89703 11.9853 4.89703H11.9921C12.2923 4.904 12.5719 4.75063 12.7561 4.47875Z"
                            fill="var(--font-color1)" />
                        </svg>
                        <svg class="emty_l_hover" width="24" height="22" viewBox="0 0 24 22" fill="none"
                          xmlns="http://www.w3.org/2000/svg">
                          <path
                            d="M11.7534 2.35249C11.576 2.19912 11.4123 2.05272 11.2486 1.89935C10.048 0.749078 8.90875 0.163484 7.67404 0.0589139C7.44892 0.0379998 7.22381 0.0310285 7.00552 0.0310285C4.87718 0.0310285 3.11038 0.909419 1.72559 2.65923C0.286236 4.47875 -0.252671 6.65381 0.108874 9.12864C0.402203 11.1294 1.2549 12.7816 2.63969 14.0365C4.74757 15.9397 6.91684 17.8428 9.01789 19.6763C9.79555 20.3595 10.5732 21.0357 11.3509 21.7189C11.4668 21.8235 11.5896 21.9002 11.7056 21.942C12.0399 22.0675 12.3468 21.9908 12.6674 21.698C13.8612 20.6453 15.055 19.5926 16.2556 18.54C17.0742 17.815 17.8996 17.0969 18.7182 16.3719C19.0047 16.1209 19.2981 15.8769 19.5777 15.6329C20.2258 15.0822 20.8943 14.5175 21.4946 13.8971C23.6229 11.6802 24.4006 9.03105 23.8071 6.03336C23.3023 3.49579 21.9517 1.69718 19.796 0.679364C17.7291 -0.296625 15.7713 -0.21994 13.9908 0.902448C13.3223 1.32073 12.7016 1.87844 12.1422 2.55466L12.074 2.63831L11.9989 2.5686C11.9171 2.49192 11.8352 2.4222 11.7534 2.35249ZM12.7561 4.47875C12.8175 4.38812 12.8789 4.2975 12.9403 4.20687C13.1586 3.87922 13.3837 3.54459 13.6566 3.25877C15.13 1.69021 17.4426 1.39044 19.4072 2.52677C20.8602 3.36334 21.7538 4.69486 22.0744 6.47953C22.5315 9.0659 21.7675 11.2689 19.7892 13.0187C18.0224 14.5802 16.2215 16.1697 14.4752 17.6964C13.7316 18.3517 12.9881 19.0001 12.2513 19.6554C12.2172 19.6902 12.1763 19.7181 12.1354 19.753L11.9989 19.8645L8.01512 16.3579C7.59218 15.9815 7.15559 15.605 6.73265 15.2425C5.79809 14.4338 4.82943 13.5973 3.90851 12.7328C2.5783 11.485 1.8211 9.77698 1.78699 7.92957C1.75288 6.17976 2.37365 4.51361 3.49921 3.36334C5.02043 1.80872 7.43528 1.39741 9.23618 2.38037C9.94563 2.77077 10.5459 3.35636 11.0576 4.17201C11.0848 4.21384 11.1121 4.25567 11.1326 4.2975C11.1667 4.36024 11.2008 4.41601 11.2417 4.47178C11.4191 4.73669 11.692 4.89703 11.9853 4.89703H11.9921C12.2923 4.904 12.5719 4.75063 12.7561 4.47875Z"
                            fill="var(--text-color)" />
                        </svg>
                      </div>
                      <p class="show_p_md">
                        мне нравится
                      </p>
                      <span class="ml-2 text-color default_">
                        {{item.likes}}
                      </span>
                      <div class="on_hov">
                        мне нравится
                      </div>
                    </button>
                  </div>
                </div>
              </div>
            </div>
            <div class="invis_part">
              <div class="flex">
                <div class="flex flex-col icons_w b_p">
                  <div class="tr_descr">{{item.annotation}}</div>
                </div>
              </div>
            </div>
          </div>
          }
        </div>

        <!-- Lectures Tab -->
        <div class="itm_a_wrap" *ngIf="activeTab === 'Лекции'">
          @for(track of sort(items); track track.id; let i = $index; let last = $last) {
          <div [ngClass]="{'widen': isOpened__[i], 'last': last}" class="article-item relative">
            <div class="vis_part">
              <div (click)="isOpened__[i] = !isOpened__[i]" class="art_img_"></div>
              <div (click)="playTrack(track); $event.stopPropagation();" class="art_img">
                @if(true) {
                <img src="assets/images/icons/play_lst.svg" alt="play">
                } @else {
                <img src="assets/images/icons/pause_lst.svg" alt="pause">
                }
              </div>
              <div (click)="isOpened__[i] = !isOpened__[i]" class="flex justify-between sp_width">
                @if(track){
                <div class="titl_w w-full">
                  <div class="article-title" (click)="openLecture(track.external_id)">
                    {{track.title}}
                  </div>
                  <div class="article-category">
                    {{ track.authorName || track.author }}</div>
                  <div class="flex items-center icons_w b_p">
                    <div class="flex items-center">
                      <div class="icon-wrap cal_w">
                        <svg width="22" height="24" viewBox="0 0 22 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path
                            d="M6.41826 5.02326C5.96059 5.02326 5.58105 4.64372 5.58105 4.18605V0.837209C5.58105 0.379535 5.96059 0 6.41826 0C6.87594 0 7.25547 0.379535 7.25547 0.837209V4.18605C7.25547 4.64372 6.87594 5.02326 6.41826 5.02326Z"
                            fill="var(--text-color)" />
                          <path
                            d="M15.3489 5.02326C14.8913 5.02326 14.5117 4.64372 14.5117 4.18605V0.837209C14.5117 0.379535 14.8913 0 15.3489 0C15.8066 0 16.1861 0.379535 16.1861 0.837209V4.18605C16.1861 4.64372 15.8066 5.02326 15.3489 5.02326Z"
                            fill="var(--text-color)" />
                          <path
                            d="M6.97663 14.7908C6.83151 14.7908 6.6864 14.7573 6.55244 14.7015C6.40733 14.6457 6.2957 14.5676 6.18407 14.4671C5.98314 14.255 5.86035 13.9759 5.86035 13.6745C5.86035 13.5294 5.89384 13.3843 5.94965 13.2503C6.00547 13.1164 6.08361 12.9936 6.18407 12.882C6.2957 12.7815 6.40733 12.7034 6.55244 12.6476C6.9543 12.4801 7.45663 12.5694 7.76919 12.882C7.97012 13.0941 8.09291 13.3843 8.09291 13.6745C8.09291 13.7415 8.08175 13.8196 8.07058 13.8978C8.05942 13.9648 8.0371 14.0317 8.00361 14.0987C7.98128 14.1657 7.94779 14.2327 7.90314 14.2996C7.86965 14.3555 7.81384 14.4113 7.76919 14.4671C7.5571 14.668 7.26686 14.7908 6.97663 14.7908Z"
                            fill="var(--text-color)" />
                          <path
                            d="M10.8839 14.7906C10.7387 14.7906 10.5936 14.7572 10.4597 14.7013C10.3146 14.6455 10.2029 14.5674 10.0913 14.4669C9.89037 14.2548 9.76758 13.9758 9.76758 13.6744C9.76758 13.5292 9.80107 13.3841 9.85688 13.2502C9.91269 13.1162 9.99083 12.9934 10.0913 12.8818C10.2029 12.7813 10.3146 12.7032 10.4597 12.6474C10.8615 12.4688 11.3639 12.5692 11.6764 12.8818C11.8773 13.0939 12.0001 13.3841 12.0001 13.6744C12.0001 13.7413 11.989 13.8195 11.9778 13.8976C11.9666 13.9646 11.9443 14.0316 11.9108 14.0986C11.8885 14.1655 11.855 14.2325 11.8104 14.2995C11.7769 14.3553 11.7211 14.4111 11.6764 14.4669C11.4643 14.6679 11.1741 14.7906 10.8839 14.7906Z"
                            fill="var(--text-color)" />
                          <path
                            d="M14.7911 14.7906C14.646 14.7906 14.5009 14.7572 14.3669 14.7013C14.2218 14.6455 14.1102 14.5674 13.9985 14.4669C13.9539 14.4111 13.9092 14.3553 13.8646 14.2995C13.8199 14.2325 13.7864 14.1655 13.7641 14.0986C13.7306 14.0316 13.7083 13.9646 13.6971 13.8976C13.686 13.8195 13.6748 13.7413 13.6748 13.6744C13.6748 13.3841 13.7976 13.0939 13.9985 12.8818C14.1102 12.7813 14.2218 12.7032 14.3669 12.6474C14.7799 12.4688 15.2711 12.5692 15.5836 12.8818C15.7846 13.0939 15.9074 13.3841 15.9074 13.6744C15.9074 13.7413 15.8962 13.8195 15.885 13.8976C15.8739 13.9646 15.8515 14.0316 15.8181 14.0986C15.7957 14.1655 15.7622 14.2325 15.7176 14.2995C15.6841 14.3553 15.6283 14.4111 15.5836 14.4669C15.3715 14.6679 15.0813 14.7906 14.7911 14.7906Z"
                            fill="var(--text-color)" />
                          <path
                            d="M6.97663 18.6976C6.83151 18.6976 6.6864 18.6642 6.55244 18.6084C6.41849 18.5526 6.2957 18.4744 6.18407 18.3739C5.98314 18.1618 5.86035 17.8716 5.86035 17.5813C5.86035 17.4362 5.89384 17.2911 5.94965 17.1572C6.00547 17.012 6.08361 16.8894 6.18407 16.7889C6.5971 16.3759 7.35617 16.3759 7.76919 16.7889C7.97012 17.001 8.09291 17.2911 8.09291 17.5813C8.09291 17.8716 7.97012 18.1618 7.76919 18.3739C7.5571 18.5748 7.26686 18.6976 6.97663 18.6976Z"
                            fill="var(--text-color)" />
                          <path
                            d="M10.8839 18.6976C10.5936 18.6976 10.3034 18.5748 10.0913 18.3739C9.89037 18.1618 9.76758 17.8716 9.76758 17.5813C9.76758 17.4362 9.80107 17.2911 9.85688 17.1572C9.91269 17.012 9.99083 16.8894 10.0913 16.7889C10.5043 16.3759 11.2634 16.3759 11.6764 16.7889C11.7769 16.8894 11.855 17.012 11.9108 17.1572C11.9666 17.2911 12.0001 17.4362 12.0001 17.5813C12.0001 17.8716 11.8773 18.1618 11.6764 18.3739C11.4643 18.5748 11.1741 18.6976 10.8839 18.6976Z"
                            fill="var(--text-color)" />
                          <path
                            d="M14.7911 18.6975C14.5009 18.6975 14.2106 18.5747 13.9985 18.3738C13.8981 18.2733 13.8199 18.1506 13.7641 18.0054C13.7083 17.8715 13.6748 17.7264 13.6748 17.5813C13.6748 17.4361 13.7083 17.291 13.7641 17.1571C13.8199 17.0119 13.8981 16.8892 13.9985 16.7887C14.2553 16.5319 14.646 16.4092 15.0032 16.4873C15.0813 16.4985 15.1483 16.5208 15.2153 16.5543C15.2822 16.5766 15.3492 16.6101 15.4162 16.6547C15.472 16.6882 15.5278 16.744 15.5836 16.7887C15.7846 17.0008 15.9074 17.291 15.9074 17.5813C15.9074 17.8715 15.7846 18.1617 15.5836 18.3738C15.3715 18.5747 15.0813 18.6975 14.7911 18.6975Z"
                            fill="var(--text-color)" />
                          <path
                            d="M20.3716 9.5886H1.39483C0.937152 9.5886 0.557617 9.20907 0.557617 8.75139C0.557617 8.29372 0.937152 7.91418 1.39483 7.91418H20.3716C20.8292 7.91418 21.2088 8.29372 21.2088 8.75139C21.2088 9.20907 20.8292 9.5886 20.3716 9.5886Z"
                            fill="var(--text-color)" />
                          <path
                            d="M15.3488 24H6.4186C2.34419 24 0 21.6558 0 17.5814V8.09304C0 4.01862 2.34419 1.67444 6.4186 1.67444H15.3488C19.4233 1.67444 21.7674 4.01862 21.7674 8.09304V17.5814C21.7674 21.6558 19.4233 24 15.3488 24ZM6.4186 3.34886C3.22605 3.34886 1.67442 4.90049 1.67442 8.09304V17.5814C1.67442 20.774 3.22605 22.3256 6.4186 22.3256H15.3488C18.5414 22.3256 20.093 20.774 20.093 17.5814V8.09304C20.093 4.90049 18.5414 3.34886 15.3488 3.34886H6.4186Z"
                            fill="var(--text-color)" />
                        </svg>
                      </div>
                      <span class="ml-2 text-color">
                        {{ track.date }}
                      </span>
                    </div>
                    <div class="audio-views flex items-center ml-[30px]">
                      <img class="mr-2" src="assets/images/icons/chck.svg" alt="">
                      <span class="ml-1 text-color">
                        прослушана {{track.views}} раз (a)
                      </span>
                    </div>
                  </div>
                </div>
                }
                <div class="actions_w" (click)="showMenu(i, $event)" [class.show_menu]="show_menu[i]">
                  <div class="flex items-center cursor-pointer icons_w show_md">
                    <div class="icon-wrap star_w no_h">
                      <svg width="7" height="22" viewBox="0 0 7 22" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="3.27778" cy="3.27778" r="2.27778" fill="#DEA53D" stroke="#DEA53D" />
                        <circle cx="3.27778" cy="11.2499" r="2.27778" fill="#DEA53D" stroke="#DEA53D" />
                        <circle cx="3.27778" cy="19.2221" r="2.27778" fill="#DEA53D" stroke="#DEA53D" />
                      </svg>
                    </div>
                  </div>
                  <div class="md_chg">
                    <div title="Добавить в очередь" class="flex items-center cursor-pointer icons_w fav_hov"
                      [ngClass]="{'in-favourites': track.id | isInPlaylist : playlists}"
                      (click)="addToPlaylist(track); $event.stopPropagation();">
                      <div class="icon-wrap">
                        <img class="list_n" src="assets/images/icons/list_n.svg" alt="quee">
                      </div>
                      <p class="show_p_md">
                        добавить в очередь
                      </p>
                      <div class="on_hov">
                        добавить в очередь
                      </div>
                    </div>
                    <div class="flex items-center cursor-pointer icons_w fav_hov"
                      [ngClass]="{'in-favourites': track.id | isInPlaylist : playlists}"
                      (click)="showPlaylistDialog(track.id); $event.stopPropagation();">
                      <div class="icon-wrap star_w">
                        <svg class="emty_f" width="25" height="24" viewBox="0 0 25 24" fill="none"
                          xmlns="http://www.w3.org/2000/svg">
                          <path
                            d="M5.96868 23.9618C5.94253 23.9618 5.91638 23.9618 5.89022 23.9618C5.36715 23.9356 4.89638 23.6999 4.53023 23.3333C4.16408 22.9143 3.98101 22.3906 4.03332 21.8145C4.05947 21.5789 4.11178 21.317 4.19024 21.0552C4.71331 19.4055 5.41946 17.2844 6.1256 15.1372C4.42562 13.9065 2.72564 12.6758 1.02565 11.4189C0.816424 11.2618 0.162584 10.7904 0.0318159 9.95249C-0.0727985 9.35022 0.0841231 8.77414 0.450273 8.35517C0.842577 7.88383 1.44411 7.62197 2.15026 7.62197C2.90871 7.62197 3.66717 7.62197 4.42562 7.62197H8.55789C8.68866 7.25537 8.79327 6.88878 8.92404 6.52218C9.08096 6.05084 9.21173 5.60568 9.36865 5.13435C9.7348 4.00837 10.0748 2.8562 10.4671 1.73022C10.5979 1.31125 10.8333 0.918472 11.1209 0.604245C11.4871 0.211462 12.0363 -0.0242083 12.5855 0.00197721C13.1348 0.0281628 13.6578 0.290018 14.024 0.735173C14.2332 1.02321 14.4163 1.33744 14.5471 1.70404C15.044 3.24899 15.5409 4.79393 16.0378 6.33888L16.4563 7.62197H17.6593C19.3332 7.62197 21.007 7.62197 22.6808 7.62197C23.047 7.62197 23.387 7.67434 23.7008 7.77909C24.2762 7.98857 24.7208 8.43372 24.9039 9.00981C25.087 9.58589 25.0085 10.1882 24.6685 10.7119C24.4854 10.9999 24.25 11.2356 23.9624 11.4451C22.2624 12.6758 20.5624 13.9327 18.8886 15.1372C19.5424 17.1273 20.1962 19.0912 20.8239 21.0813C21.0593 21.7884 21.0332 22.3906 20.7455 22.9405C20.327 23.7785 19.3593 24.1974 18.4701 23.9094C18.1301 23.8046 17.8163 23.6475 17.5286 23.438C16.1424 22.443 14.7563 21.4218 13.344 20.4005L12.5071 19.7983L11.5132 20.5314C10.1794 21.5003 8.84558 22.4954 7.51174 23.4642C6.96252 23.7785 6.4656 23.9618 5.96868 23.9618ZM3.45794 9.14073C3.01333 9.14073 2.59487 9.14073 2.15026 9.14073C1.91487 9.14073 1.7318 9.21929 1.65334 9.32403C1.57488 9.40259 1.54872 9.53352 1.57488 9.66445C1.60103 9.76919 1.65334 9.90012 1.94103 10.1096C3.71947 11.3927 5.47176 12.702 7.25021 13.9851C7.69482 14.3255 7.85174 14.7968 7.69482 15.3205C6.96252 17.5463 6.23022 19.7983 5.68099 21.5003C5.62869 21.6312 5.60253 21.7884 5.60253 21.8931C5.60253 22.0502 5.62869 22.155 5.70715 22.2597C5.75945 22.3383 5.86407 22.3644 5.94253 22.3644C6.0733 22.3644 6.23022 22.3383 6.54406 22.1026C7.87789 21.1337 9.21173 20.1387 10.5456 19.1698L12.4809 17.7558L14.2594 19.0389C15.6455 20.0601 17.0317 21.0552 18.4178 22.0764C18.5747 22.1811 18.7316 22.2597 18.9147 22.3121C19.0716 22.3644 19.2286 22.2859 19.307 22.1288C19.3593 22.024 19.4116 21.8407 19.2809 21.4741C18.6009 19.4055 17.9209 17.3368 17.2409 15.2682C17.0578 14.7183 17.2147 14.2731 17.6593 13.9327C19.4116 12.6758 21.1901 11.3665 22.9424 10.0834C23.0731 9.97867 23.1777 9.87393 23.2824 9.743C23.3608 9.63826 23.387 9.50733 23.3347 9.40259C23.3085 9.29785 23.2039 9.21929 23.0993 9.16692C22.9685 9.11455 22.7854 9.08836 22.6024 9.08836C20.9285 9.08836 19.2547 9.08836 17.5809 9.08836H15.2794L15.0963 8.59084C15.0701 8.51228 15.044 8.45991 15.0178 8.40754L14.4948 6.78404C13.9978 5.23909 13.5009 3.69414 13.004 2.14919C12.9517 1.96589 12.8471 1.78259 12.7425 1.65167C12.664 1.54692 12.5332 1.52074 12.4809 1.52074C12.4286 1.52074 12.324 1.52074 12.2455 1.62548C12.1148 1.75641 12.0102 1.96589 11.9317 2.17538C11.5656 3.30136 11.1994 4.45352 10.8333 5.55331C10.6763 6.02465 10.5456 6.46981 10.3886 6.94115C10.2579 7.30775 10.1533 7.70053 10.0225 8.06713L9.68249 9.14073H4.42562C4.11178 9.14073 3.77178 9.14073 3.45794 9.14073Z"
                            fill="var(--font-color1)" />
                        </svg>
                        <svg class="emty_f_hover" width="25" height="24" viewBox="0 0 25 24" fill="none"
                          xmlns="http://www.w3.org/2000/svg">
                          <path
                            d="M5.96868 23.9618C5.94253 23.9618 5.91638 23.9618 5.89022 23.9618C5.36715 23.9356 4.89638 23.6999 4.53023 23.3333C4.16408 22.9143 3.98101 22.3906 4.03332 21.8145C4.05947 21.5789 4.11178 21.317 4.19024 21.0552C4.71331 19.4055 5.41946 17.2844 6.1256 15.1372C4.42562 13.9065 2.72564 12.6758 1.02565 11.4189C0.816424 11.2618 0.162584 10.7904 0.0318159 9.95249C-0.0727985 9.35022 0.0841231 8.77414 0.450273 8.35517C0.842577 7.88383 1.44411 7.62197 2.15026 7.62197C2.90871 7.62197 3.66717 7.62197 4.42562 7.62197H8.55789C8.68866 7.25537 8.79327 6.88878 8.92404 6.52218C9.08096 6.05084 9.21173 5.60568 9.36865 5.13435C9.7348 4.00837 10.0748 2.8562 10.4671 1.73022C10.5979 1.31125 10.8333 0.918472 11.1209 0.604245C11.4871 0.211462 12.0363 -0.0242083 12.5855 0.00197721C13.1348 0.0281628 13.6578 0.290018 14.024 0.735173C14.2332 1.02321 14.4163 1.33744 14.5471 1.70404C15.044 3.24899 15.5409 4.79393 16.0378 6.33888L16.4563 7.62197H17.6593C19.3332 7.62197 21.007 7.62197 22.6808 7.62197C23.047 7.62197 23.387 7.67434 23.7008 7.77909C24.2762 7.98857 24.7208 8.43372 24.9039 9.00981C25.087 9.58589 25.0085 10.1882 24.6685 10.7119C24.4854 10.9999 24.25 11.2356 23.9624 11.4451C22.2624 12.6758 20.5624 13.9327 18.8886 15.1372C19.5424 17.1273 20.1962 19.0912 20.8239 21.0813C21.0593 21.7884 21.0332 22.3906 20.7455 22.9405C20.327 23.7785 19.3593 24.1974 18.4701 23.9094C18.1301 23.8046 17.8163 23.6475 17.5286 23.438C16.1424 22.443 14.7563 21.4218 13.344 20.4005L12.5071 19.7983L11.5132 20.5314C10.1794 21.5003 8.84558 22.4954 7.51174 23.4642C6.96252 23.7785 6.4656 23.9618 5.96868 23.9618ZM3.45794 9.14073C3.01333 9.14073 2.59487 9.14073 2.15026 9.14073C1.91487 9.14073 1.7318 9.21929 1.65334 9.32403C1.57488 9.40259 1.54872 9.53352 1.57488 9.66445C1.60103 9.76919 1.65334 9.90012 1.94103 10.1096C3.71947 11.3927 5.47176 12.702 7.25021 13.9851C7.69482 14.3255 7.85174 14.7968 7.69482 15.3205C6.96252 17.5463 6.23022 19.7983 5.68099 21.5003C5.62869 21.6312 5.60253 21.7884 5.60253 21.8931C5.60253 22.0502 5.62869 22.155 5.70715 22.2597C5.75945 22.3383 5.86407 22.3644 5.94253 22.3644C6.0733 22.3644 6.23022 22.3383 6.54406 22.1026C7.87789 21.1337 9.21173 20.1387 10.5456 19.1698L12.4809 17.7558L14.2594 19.0389C15.6455 20.0601 17.0317 21.0552 18.4178 22.0764C18.5747 22.1811 18.7316 22.2597 18.9147 22.3121C19.0716 22.3644 19.2286 22.2859 19.307 22.1288C19.3593 22.024 19.4116 21.8407 19.2809 21.4741C18.6009 19.4055 17.9209 17.3368 17.2409 15.2682C17.0578 14.7183 17.2147 14.2731 17.6593 13.9327C19.4116 12.6758 21.1901 11.3665 22.9424 10.0834C23.0731 9.97867 23.1777 9.87393 23.2824 9.743C23.3608 9.63826 23.387 9.50733 23.3347 9.40259C23.3085 9.29785 23.2039 9.21929 23.0993 9.16692C22.9685 9.11455 22.7854 9.08836 22.6024 9.08836C20.9285 9.08836 19.2547 9.08836 17.5809 9.08836H15.2794L15.0963 8.59084C15.0701 8.51228 15.044 8.45991 15.0178 8.40754L14.4948 6.78404C13.9978 5.23909 13.5009 3.69414 13.004 2.14919C12.9517 1.96589 12.8471 1.78259 12.7425 1.65167C12.664 1.54692 12.5332 1.52074 12.4809 1.52074C12.4286 1.52074 12.324 1.52074 12.2455 1.62548C12.1148 1.75641 12.0102 1.96589 11.9317 2.17538C11.5656 3.30136 11.1994 4.45352 10.8333 5.55331C10.6763 6.02465 10.5456 6.46981 10.3886 6.94115C10.2579 7.30775 10.1533 7.70053 10.0225 8.06713L9.68249 9.14073H4.42562C4.11178 9.14073 3.77178 9.14073 3.45794 9.14073Z"
                            fill="var(--text-color)" />
                        </svg>
                      </div>
                      <p class="show_p_md">
                        добавить в избранное
                      </p>
                      <div class="on_hov">
                        добавить в избранное
                      </div>
                    </div>
                    <div class="flex items-center cursor-pointer icons_w shr_hov"
                      (click)="shareTrack(track); $event.stopPropagation();">
                      <div class="icon-wrap share_w">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path
                            d="M15.9238 7.01009C13.5066 8.21941 11.0965 9.42157 8.66504 10.638C8.9368 11.5325 8.94395 12.427 8.66504 13.3357C11.0822 14.5451 13.4923 15.7544 15.8737 16.9422C16.2456 16.5988 16.5745 16.2267 16.9607 15.9476C19.6353 13.9941 23.4542 15.6113 23.9334 18.8957C24.3481 21.7509 22.0168 24.2268 19.1347 23.9835C17.0894 23.8117 15.3731 22.1516 15.1514 20.1051C15.1085 19.6829 15.1156 19.2464 15.1729 18.8313C15.2086 18.588 15.1442 18.5093 14.9512 18.4163C12.8772 17.3859 10.8105 16.3483 8.73656 15.3107C8.50056 15.1891 8.25741 15.0817 8.02141 14.9458C7.87838 14.8671 7.79972 14.8885 7.6853 15.0102C6.76276 15.9834 5.63283 16.4556 4.28836 16.4413C2.21445 16.4127 0.326467 14.7454 0.0475605 12.6846C-0.288557 10.2588 1.18464 8.12638 3.58752 7.63979C5.16084 7.32494 6.52676 7.79722 7.65669 8.94213C7.80687 9.09241 7.89984 9.10672 8.07862 9.01369C10.3742 7.85447 12.677 6.70955 14.9798 5.56464C15.1585 5.47877 15.2158 5.40005 15.1729 5.18538C14.7295 2.75959 16.4458 0.39105 18.8773 0.047575C21.4232 -0.317367 23.7117 1.45725 23.9763 3.99753C24.2266 6.37323 22.4673 8.57004 20.093 8.8348C18.4196 9.028 17.0751 8.41977 16.0239 7.12458C15.9881 7.09596 15.9595 7.06018 15.9238 7.01009ZM7.14179 11.9976C7.14179 10.5021 5.91174 9.26414 4.42424 9.2713C2.94389 9.27845 1.721 10.4949 1.70669 11.9762C1.69239 13.4574 2.92244 14.7025 4.41709 14.7168C5.90459 14.7311 7.14179 13.5003 7.14179 11.9976ZM16.832 19.5541C16.8248 21.0496 18.0549 22.2876 19.5424 22.2804C21.0227 22.2804 22.2671 21.0353 22.2671 19.5541C22.2599 18.0728 21.0513 16.8635 19.5567 16.8492C18.062 16.8277 16.8391 18.0442 16.832 19.5541ZM19.5424 7.14605C21.037 7.14605 22.2671 5.91527 22.2671 4.42688C22.2599 2.93849 21.0156 1.70055 19.5352 1.7077C18.0477 1.71486 16.8463 2.93133 16.832 4.41972C16.8248 5.92242 18.0406 7.14605 19.5424 7.14605Z"
                            fill="var(--font-color1)" />
                        </svg>
                      </div>
                      <p class="show_p_md">
                        поделиться
                      </p>
                      <div class="on_hov">
                        поделиться
                      </div>
                    </div>
                    <button class="flex items-center icons_w lik_hov" [ngClass]="{'is-liked': track.liked}"
                      (click)="likeTrack(track); $event.stopPropagation();">
                      <div class="icon-wrap like_w">
                        <svg class="emty_l" width="24" height="22" viewBox="0 0 24 22" fill="none"
                          xmlns="http://www.w3.org/2000/svg">
                          <path
                            d="M11.7534 2.35249C11.576 2.19912 11.4123 2.05272 11.2486 1.89935C10.048 0.749078 8.90875 0.163484 7.67404 0.0589139C7.44892 0.0379998 7.22381 0.0310285 7.00552 0.0310285C4.87718 0.0310285 3.11038 0.909419 1.72559 2.65923C0.286236 4.47875 -0.252671 6.65381 0.108874 9.12864C0.402203 11.1294 1.2549 12.7816 2.63969 14.0365C4.74757 15.9397 6.91684 17.8428 9.01789 19.6763C9.79555 20.3595 10.5732 21.0357 11.3509 21.7189C11.4668 21.8235 11.5896 21.9002 11.7056 21.942C12.0399 22.0675 12.3468 21.9908 12.6674 21.698C13.8612 20.6453 15.055 19.5926 16.2556 18.54C17.0742 17.815 17.8996 17.0969 18.7182 16.3719C19.0047 16.1209 19.2981 15.8769 19.5777 15.6329C20.2258 15.0822 20.8943 14.5175 21.4946 13.8971C23.6229 11.6802 24.4006 9.03105 23.8071 6.03336C23.3023 3.49579 21.9517 1.69718 19.796 0.679364C17.7291 -0.296625 15.7713 -0.21994 13.9908 0.902448C13.3223 1.32073 12.7016 1.87844 12.1422 2.55466L12.074 2.63831L11.9989 2.5686C11.9171 2.49192 11.8352 2.4222 11.7534 2.35249ZM12.7561 4.47875C12.8175 4.38812 12.8789 4.2975 12.9403 4.20687C13.1586 3.87922 13.3837 3.54459 13.6566 3.25877C15.13 1.69021 17.4426 1.39044 19.4072 2.52677C20.8602 3.36334 21.7538 4.69486 22.0744 6.47953C22.5315 9.0659 21.7675 11.2689 19.7892 13.0187C18.0224 14.5802 16.2215 16.1697 14.4752 17.6964C13.7316 18.3517 12.9881 19.0001 12.2513 19.6554C12.2172 19.6902 12.1763 19.7181 12.1354 19.753L11.9989 19.8645L8.01512 16.3579C7.59218 15.9815 7.15559 15.605 6.73265 15.2425C5.79809 14.4338 4.82943 13.5973 3.90851 12.7328C2.5783 11.485 1.8211 9.77698 1.78699 7.92957C1.75288 6.17976 2.37365 4.51361 3.49921 3.36334C5.02043 1.80872 7.43528 1.39741 9.23618 2.38037C9.94563 2.77077 10.5459 3.35636 11.0576 4.17201C11.0848 4.21384 11.1121 4.25567 11.1326 4.2975C11.1667 4.36024 11.2008 4.41601 11.2417 4.47178C11.4191 4.73669 11.692 4.89703 11.9853 4.89703H11.9921C12.2923 4.904 12.5719 4.75063 12.7561 4.47875Z"
                            fill="var(--font-color1)" />
                        </svg>
                        <svg class="emty_l_hover" width="24" height="22" viewBox="0 0 24 22" fill="none"
                          xmlns="http://www.w3.org/2000/svg">
                          <path
                            d="M11.7534 2.35249C11.576 2.19912 11.4123 2.05272 11.2486 1.89935C10.048 0.749078 8.90875 0.163484 7.67404 0.0589139C7.44892 0.0379998 7.22381 0.0310285 7.00552 0.0310285C4.87718 0.0310285 3.11038 0.909419 1.72559 2.65923C0.286236 4.47875 -0.252671 6.65381 0.108874 9.12864C0.402203 11.1294 1.2549 12.7816 2.63969 14.0365C4.74757 15.9397 6.91684 17.8428 9.01789 19.6763C9.79555 20.3595 10.5732 21.0357 11.3509 21.7189C11.4668 21.8235 11.5896 21.9002 11.7056 21.942C12.0399 22.0675 12.3468 21.9908 12.6674 21.698C13.8612 20.6453 15.055 19.5926 16.2556 18.54C17.0742 17.815 17.8996 17.0969 18.7182 16.3719C19.0047 16.1209 19.2981 15.8769 19.5777 15.6329C20.2258 15.0822 20.8943 14.5175 21.4946 13.8971C23.6229 11.6802 24.4006 9.03105 23.8071 6.03336C23.3023 3.49579 21.9517 1.69718 19.796 0.679364C17.7291 -0.296625 15.7713 -0.21994 13.9908 0.902448C13.3223 1.32073 12.7016 1.87844 12.1422 2.55466L12.074 2.63831L11.9989 2.5686C11.9171 2.49192 11.8352 2.4222 11.7534 2.35249ZM12.7561 4.47875C12.8175 4.38812 12.8789 4.2975 12.9403 4.20687C13.1586 3.87922 13.3837 3.54459 13.6566 3.25877C15.13 1.69021 17.4426 1.39044 19.4072 2.52677C20.8602 3.36334 21.7538 4.69486 22.0744 6.47953C22.5315 9.0659 21.7675 11.2689 19.7892 13.0187C18.0224 14.5802 16.2215 16.1697 14.4752 17.6964C13.7316 18.3517 12.9881 19.0001 12.2513 19.6554C12.2172 19.6902 12.1763 19.7181 12.1354 19.753L11.9989 19.8645L8.01512 16.3579C7.59218 15.9815 7.15559 15.605 6.73265 15.2425C5.79809 14.4338 4.82943 13.5973 3.90851 12.7328C2.5783 11.485 1.8211 9.77698 1.78699 7.92957C1.75288 6.17976 2.37365 4.51361 3.49921 3.36334C5.02043 1.80872 7.43528 1.39741 9.23618 2.38037C9.94563 2.77077 10.5459 3.35636 11.0576 4.17201C11.0848 4.21384 11.1121 4.25567 11.1326 4.2975C11.1667 4.36024 11.2008 4.41601 11.2417 4.47178C11.4191 4.73669 11.692 4.89703 11.9853 4.89703H11.9921C12.2923 4.904 12.5719 4.75063 12.7561 4.47875Z"
                            fill="var(--text-color)" />
                        </svg>
                      </div>
                      <p class="show_p_md">
                        мне нравится
                      </p>
                      <span class="ml-2 text-color default_">
                        {{track.likes}}
                      </span>
                      <div class="on_hov">
                        мне нравится
                      </div>
                    </button>
                  </div>
                </div>
              </div>
              <div class="flex bs_wrapper_">
                @if(track.text_link) {
                <p (click)="openLecture(track.external_id)" class="btn_item_wrapper">
                  <span class="btn_item_">Читать</span>
                </p>
                }
                @if(track.youtube) {
                <p (click)="openLecture(track.external_id)" class="btn_item_wrapper">
                  <span class="btn_item_">Смотреть</span>
                </p>
                }
              </div>
            </div>
            <div class="flex bs_wrapper_ vis_md">
              @if(track.text_link) {
              <p (click)="openLecture(track.external_id)" class="btn_item_wrapper">
                <span class="btn_item_">Читать</span>
              </p>
              }
              @if(track.youtube) {
              <p (click)="openLecture(track.external_id)" class="btn_item_wrapper">
                <span class="btn_item_">Смотреть</span>
              </p>
              }
            </div>
            <div class="invis_part">

              <div class="flex">
                <div class="flex flex-col icons_w b_p">
                  <div class="tr_descr">{{ track.description }}</div>
                  <span class="mt-u_ text-color">Длительность: {{Math.ceil(track.duration / 60)}} мин.</span>
                </div>
              </div>
            </div>
          </div>
          }
        </div>

        <div class="search-content" *ngIf="activeTab === 'Фото'">
          <ng-container *ngTemplateOutlet="photoList; context: {photoList: items}"></ng-container>
        </div>

        <div class="search-content" *ngIf="activeTab === 'Форум'">
          <div class="forum-list flex-col">
            @for(topic of sort(items); track topic.id) {
            <ng-container *ngTemplateOutlet="topicItem; context: {topic: topic}"></ng-container>
            }
          </div>
        </div>
      </div>
      <div class="buttn_catg" *ngIf="page < totalPages">
        <button class="load-more-button" (click)="nextPage()" [disabled]="false">
          <span>Загрузить еще</span>
        </button>
      </div>
    </ng-container>


  </div>
</div>


<ng-template #photoList let-photoList="photoList">
  <div class="photo-list">
    @for(photo of photoList; track photo.id) {
    @for(item of photo.photos; track item.id) {
    <div class="photo-item">
      <div class="photo-item-info">
        {{photo.description || ''}}
        <div class="flex justify-between photo-item-actions">
          <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"
            (click)="router.navigate(['/ru/photo/' + photo.slug])">
            <path
              d="M1.40915 13.6386C1.73558 13.2422 2.03285 12.8867 2.3243 12.5312C2.67404 12.1115 3.01795 11.6919 3.36769 11.2722C4.39942 10.0424 6.22971 10.0249 7.29058 11.2314C7.59952 11.5811 7.88514 11.9483 8.19408 12.3213C8.57879 11.8784 8.95767 11.4471 9.33655 11.0158C9.82036 10.4621 10.2983 9.90836 10.7821 9.35466C11.8547 8.13069 13.6966 8.11903 14.7808 9.34301C15.9583 10.6719 17.1182 12.0124 18.284 13.3471C18.3773 13.4521 18.4647 13.5511 18.6046 13.7085C18.6046 13.5745 18.6046 13.5045 18.6046 13.4287C18.6046 12.3213 18.6046 11.2198 18.6104 10.1124C18.6104 9.97248 18.6338 9.82094 18.6921 9.69271C18.8203 9.40712 19.1467 9.24975 19.4323 9.31386C19.7529 9.3838 19.9861 9.65191 19.9919 9.97831C19.9977 10.4504 19.9977 10.9225 19.9977 11.3946C19.9977 12.776 19.9977 14.1573 19.9977 15.5386C19.9919 17.8467 18.3656 19.6885 16.0748 19.9741C15.8825 19.9974 15.6843 19.9974 15.4861 19.9974C11.8314 19.9974 8.17076 20.0032 4.51599 19.9974C2.37093 19.9974 0.616414 18.651 0.12678 16.6169C0.0451745 16.273 0.00437173 15.9117 0.00437173 15.562C-0.00145724 11.8726 -0.00145724 8.17731 0.00437173 4.48791C0.00437173 2.28475 1.44413 0.512904 3.58336 0.0815983C3.86898 0.0233138 4.16043 0.00582845 4.45188 0.00582845C6.58528 0 8.71868 0 10.8579 0C11.3009 0 11.5982 0.250623 11.6215 0.635301C11.6448 1.01415 11.4117 1.31723 11.0328 1.37551C10.9395 1.393 10.8463 1.393 10.753 1.393C8.66039 1.393 6.56196 1.38717 4.46936 1.393C2.91303 1.40466 1.7006 2.42464 1.43247 3.95752C1.3975 4.14403 1.3975 4.33637 1.3975 4.52288C1.3975 7.47207 1.3975 10.4271 1.3975 13.3763C1.3975 13.4462 1.40332 13.5162 1.40915 13.6386ZM9.9894 18.6102V18.6044C11.9013 18.6044 13.8132 18.6277 15.7251 18.5986C17.1415 18.5753 18.3831 17.3688 18.5172 15.9583C18.5288 15.8592 18.4822 15.731 18.4181 15.6552C17.9518 15.1073 17.4738 14.5711 16.9958 14.0291C15.9058 12.7876 14.8158 11.552 13.7316 10.3105C13.4693 10.0074 13.1545 9.82677 12.7465 9.83259C12.356 9.83259 12.047 9.99579 11.7964 10.2872C10.7705 11.4646 9.74458 12.6477 8.71868 13.8251C8.36312 14.2272 7.90846 14.2214 7.56455 13.8076C7.14486 13.3063 6.72517 12.8051 6.31132 12.298C5.73425 11.5986 4.89488 11.5986 4.32364 12.298C3.43181 13.3879 2.53997 14.4837 1.63065 15.562C1.44413 15.7834 1.40915 15.9874 1.47327 16.2497C1.79969 17.6602 3.00046 18.6161 4.47519 18.6219C6.31132 18.6102 8.14744 18.6102 9.9894 18.6102Z"
              fill="white" />
            <path
              d="M18.4799 5.4438C18.8996 5.84596 19.331 6.24813 19.739 6.66778C19.8556 6.78435 19.9488 6.95337 19.9838 7.11074C20.0479 7.41965 19.8847 7.69358 19.6166 7.83347C19.3426 7.96752 19.0453 7.92672 18.8122 7.69941C18.4216 7.32056 18.0311 6.93588 17.6522 6.54538C17.5415 6.42881 17.4657 6.42298 17.3258 6.50458C16.0959 7.17485 14.866 7.16319 13.6885 6.41132C12.546 5.67111 12.0156 4.57536 12.103 3.21733C12.2138 1.43966 13.7118 0.0466576 15.5188 0.00585846C17.2966 -0.0349407 18.8238 1.29395 19.0395 3.04831C19.1444 3.89343 18.9521 4.67445 18.4799 5.4438ZM15.6062 1.39886C14.4579 1.3872 13.5078 2.3081 13.4962 3.44464C13.4845 4.61616 14.3996 5.5662 15.5713 5.58368C16.7137 5.60117 17.6755 4.65113 17.6872 3.50876C17.6872 2.3489 16.7662 1.41051 15.6062 1.39886Z"
              fill="white" />
            <path
              d="M2.79707 5.36801C2.77959 3.96336 3.91624 2.80349 5.32102 2.79184C6.73746 2.78018 7.89159 3.90507 7.90908 5.30973C7.92074 6.73187 6.78992 7.89756 5.37931 7.90922C3.95704 7.91504 2.80873 6.79015 2.79707 5.36801ZM5.36182 4.19067C4.70898 4.18484 4.19603 4.69191 4.1902 5.33887C4.18437 5.98583 4.69732 6.51039 5.34433 6.51039C5.98552 6.51039 6.51013 5.98583 6.51013 5.3447C6.51013 4.7094 5.99718 4.19067 5.36182 4.19067Z"
              fill="white" />
          </svg>
          <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M13.2698 5.84174C11.2555 6.84951 9.24711 7.85131 7.22087 8.86503C7.44733 9.61042 7.45329 10.3558 7.22087 11.1131C9.23519 12.1209 11.2436 13.1287 13.2281 14.1185C13.538 13.8323 13.8121 13.5222 14.1339 13.2897C16.3628 11.6617 19.5452 13.0094 19.9445 15.7465C20.2901 18.1257 18.3473 20.189 15.9456 19.9862C14.2412 19.8431 12.8109 18.4597 12.6262 16.7542C12.5904 16.4024 12.5964 16.0386 12.644 15.6928C12.6738 15.49 12.6202 15.4244 12.4593 15.3469C10.731 14.4882 9.00873 13.6236 7.28046 12.7589C7.0838 12.6576 6.88118 12.5681 6.68451 12.4548C6.56532 12.3892 6.49977 12.4071 6.40441 12.5085C5.63563 13.3195 4.69403 13.713 3.57364 13.7011C1.84537 13.6773 0.272056 12.2879 0.0396338 10.5705C-0.240464 8.54899 0.987199 6.77199 2.9896 6.36649C4.3007 6.10412 5.43897 6.49768 6.38058 7.45178C6.50573 7.577 6.5832 7.58893 6.73219 7.51141C8.6452 6.54539 10.5642 5.59129 12.4831 4.6372C12.6321 4.56564 12.6798 4.50004 12.644 4.32115C12.2746 2.29966 13.7048 0.325875 15.7311 0.0396458C17.8527 -0.264472 19.7597 1.21438 19.9802 3.33128C20.1888 5.31103 18.7228 7.1417 16.7442 7.36233C15.3497 7.52334 14.2293 7.01647 13.3532 5.93715C13.3234 5.9133 13.2996 5.88348 13.2698 5.84174ZM5.95149 9.99802C5.95149 8.75173 4.92645 7.72012 3.68687 7.72608C2.45324 7.73205 1.43416 8.74577 1.42225 9.98013C1.41033 11.2145 2.43537 12.2521 3.68091 12.264C4.92049 12.2759 5.95149 11.2503 5.95149 9.99802ZM14.0267 16.2951C14.0207 17.5413 15.0457 18.573 16.2853 18.567C17.5189 18.567 18.5559 17.5294 18.5559 16.2951C18.5499 15.0607 17.5428 14.0529 16.2972 14.041C15.0517 14.0231 14.0326 15.0368 14.0267 16.2951ZM16.2853 5.95504C17.5309 5.95504 18.5559 4.92939 18.5559 3.68906C18.5499 2.44874 17.513 1.41712 16.2794 1.42308C15.0398 1.42905 14.0386 2.44278 14.0267 3.6831C14.0207 4.93535 15.0338 5.95504 16.2853 5.95504Z"
              fill="white" />
          </svg>

          <button class="flex items-center icons_w lik_hov" [ngClass]="{'is-liked': item.liked}"
            (click)="likePhoto(item); $event.stopPropagation();">
            <div class="icon-wrap like_w">
              <svg class="emty_l" width="24" height="22" viewBox="0 0 24 22" fill="none"
                xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M11.7534 2.35249C11.576 2.19912 11.4123 2.05272 11.2486 1.89935C10.048 0.749078 8.90875 0.163484 7.67404 0.0589139C7.44892 0.0379998 7.22381 0.0310285 7.00552 0.0310285C4.87718 0.0310285 3.11038 0.909419 1.72559 2.65923C0.286236 4.47875 -0.252671 6.65381 0.108874 9.12864C0.402203 11.1294 1.2549 12.7816 2.63969 14.0365C4.74757 15.9397 6.91684 17.8428 9.01789 19.6763C9.79555 20.3595 10.5732 21.0357 11.3509 21.7189C11.4668 21.8235 11.5896 21.9002 11.7056 21.942C12.0399 22.0675 12.3468 21.9908 12.6674 21.698C13.8612 20.6453 15.055 19.5926 16.2556 18.54C17.0742 17.815 17.8996 17.0969 18.7182 16.3719C19.0047 16.1209 19.2981 15.8769 19.5777 15.6329C20.2258 15.0822 20.8943 14.5175 21.4946 13.8971C23.6229 11.6802 24.4006 9.03105 23.8071 6.03336C23.3023 3.49579 21.9517 1.69718 19.796 0.679364C17.7291 -0.296625 15.7713 -0.21994 13.9908 0.902448C13.3223 1.32073 12.7016 1.87844 12.1422 2.55466L12.074 2.63831L11.9989 2.5686C11.9171 2.49192 11.8352 2.4222 11.7534 2.35249ZM12.7561 4.47875C12.8175 4.38812 12.8789 4.2975 12.9403 4.20687C13.1586 3.87922 13.3837 3.54459 13.6566 3.25877C15.13 1.69021 17.4426 1.39044 19.4072 2.52677C20.8602 3.36334 21.7538 4.69486 22.0744 6.47953C22.5315 9.0659 21.7675 11.2689 19.7892 13.0187C18.0224 14.5802 16.2215 16.1697 14.4752 17.6964C13.7316 18.3517 12.9881 19.0001 12.2513 19.6554C12.2172 19.6902 12.1763 19.7181 12.1354 19.753L11.9989 19.8645L8.01512 16.3579C7.59218 15.9815 7.15559 15.605 6.73265 15.2425C5.79809 14.4338 4.82943 13.5973 3.90851 12.7328C2.5783 11.485 1.8211 9.77698 1.78699 7.92957C1.75288 6.17976 2.37365 4.51361 3.49921 3.36334C5.02043 1.80872 7.43528 1.39741 9.23618 2.38037C9.94563 2.77077 10.5459 3.35636 11.0576 4.17201C11.0848 4.21384 11.1121 4.25567 11.1326 4.2975C11.1667 4.36024 11.2008 4.41601 11.2417 4.47178C11.4191 4.73669 11.692 4.89703 11.9853 4.89703H11.9921C12.2923 4.904 12.5719 4.75063 12.7561 4.47875Z"
                  fill="var(--font-color1)" />
              </svg>
              <svg class="emty_l_hover" width="24" height="22" viewBox="0 0 24 22" fill="none"
                xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M11.7534 2.35249C11.576 2.19912 11.4123 2.05272 11.2486 1.89935C10.048 0.749078 8.90875 0.163484 7.67404 0.0589139C7.44892 0.0379998 7.22381 0.0310285 7.00552 0.0310285C4.87718 0.0310285 3.11038 0.909419 1.72559 2.65923C0.286236 4.47875 -0.252671 6.65381 0.108874 9.12864C0.402203 11.1294 1.2549 12.7816 2.63969 14.0365C4.74757 15.9397 6.91684 17.8428 9.01789 19.6763C9.79555 20.3595 10.5732 21.0357 11.3509 21.7189C11.4668 21.8235 11.5896 21.9002 11.7056 21.942C12.0399 22.0675 12.3468 21.9908 12.6674 21.698C13.8612 20.6453 15.055 19.5926 16.2556 18.54C17.0742 17.815 17.8996 17.0969 18.7182 16.3719C19.0047 16.1209 19.2981 15.8769 19.5777 15.6329C20.2258 15.0822 20.8943 14.5175 21.4946 13.8971C23.6229 11.6802 24.4006 9.03105 23.8071 6.03336C23.3023 3.49579 21.9517 1.69718 19.796 0.679364C17.7291 -0.296625 15.7713 -0.21994 13.9908 0.902448C13.3223 1.32073 12.7016 1.87844 12.1422 2.55466L12.074 2.63831L11.9989 2.5686C11.9171 2.49192 11.8352 2.4222 11.7534 2.35249ZM12.7561 4.47875C12.8175 4.38812 12.8789 4.2975 12.9403 4.20687C13.1586 3.87922 13.3837 3.54459 13.6566 3.25877C15.13 1.69021 17.4426 1.39044 19.4072 2.52677C20.8602 3.36334 21.7538 4.69486 22.0744 6.47953C22.5315 9.0659 21.7675 11.2689 19.7892 13.0187C18.0224 14.5802 16.2215 16.1697 14.4752 17.6964C13.7316 18.3517 12.9881 19.0001 12.2513 19.6554C12.2172 19.6902 12.1763 19.7181 12.1354 19.753L11.9989 19.8645L8.01512 16.3579C7.59218 15.9815 7.15559 15.605 6.73265 15.2425C5.79809 14.4338 4.82943 13.5973 3.90851 12.7328C2.5783 11.485 1.8211 9.77698 1.78699 7.92957C1.75288 6.17976 2.37365 4.51361 3.49921 3.36334C5.02043 1.80872 7.43528 1.39741 9.23618 2.38037C9.94563 2.77077 10.5459 3.35636 11.0576 4.17201C11.0848 4.21384 11.1121 4.25567 11.1326 4.2975C11.1667 4.36024 11.2008 4.41601 11.2417 4.47178C11.4191 4.73669 11.692 4.89703 11.9853 4.89703H11.9921C12.2923 4.904 12.5719 4.75063 12.7561 4.47875Z"
                  fill="var(--text-color)" />
              </svg>
            </div>
            <span class="ml-2 text-color default_">{{item.likes}}</span>
          </button>

          <button class="btn-favourite mr-3" [ngClass]="{'in-favourites': item.inFavourites}"
            (click)="favoritePhoto(item)">
            <svg fill="transparent" stroke="white" stroke-width="20px" height="800px" width="800px" version="1.1"
              id="Capa_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
              viewBox="0 0 489.914 489.914" xml:space="preserve">
              <path
                d="M483.871,204.522c5.38-4.725,7.395-12.197,5.123-18.98c-2.272-6.785-8.394-11.538-15.53-12.07l-142.249-10.618
                      c-6.607-0.502-12.376-4.609-14.982-10.682L259.738,21.184c-2.838-6.555-9.331-10.793-16.484-10.714
                      c-7.137,0.066-13.549,4.401-16.259,11.021l-54.056,132.016c-2.497,6.125-8.204,10.346-14.792,10.956l-142.04,13.245
                      c-7.117,0.66-13.146,5.527-15.29,12.343c-2.142,6.817,0.017,14.263,5.461,18.884l108.845,92.216
                      c5.046,4.288,7.299,11.005,5.851,17.452L89.682,457.786c-1.565,6.978,1.19,14.212,7.023,18.369c5.834,4.141,13.566,4.4,19.658,0.627
                      l121.315-75.039c5.624-3.477,12.715-3.545,18.417-0.159l122.686,72.767c6.14,3.642,13.888,3.256,19.624-0.998
                      c5.738-4.254,8.381-11.555,6.671-18.496l-33.839-138.575c-1.579-6.43,0.547-13.198,5.511-17.561L483.871,204.522z" />
            </svg>
          </button>
        </div>
      </div>
      {{item.id}}
      <!-- <img [src]="'https://images.pexels.com/photos/3680219/pexels-photo-3680219.jpeg'" [alt]="item.description"> -->
      <img [src]="(environment.serverUrl + '/upload/' + photo.name)" [alt]="photo.description || ''">
    </div>
    }
    }
  </div>
</ng-template>

<ng-template #topicItem let-topic="topic">
  <div class="flex forum-item justify-between gap-2 w-full">
    <div class="forum-main-content justify-between gap-[40px] w-full">
      <div class="flex">
        <div class="flex flex-col gap-2">
          <a [routerLink]="'/ru/forum/topic/' + topic?.id" class="forum-name">
            {{topic?.name}}
          </a>
          <div class="forum-type">
            {{topic?.category?.name}}
          </div>
          <div class="forum-last-answer pt-3" *ngIf="topic?.comments.length">
            Последний ответ: <span>{{formatDate(topic?.comments[topic?.comments.length-1].createdAt)}} от
              {{topic?.comments[topic?.comments.length-1]?.user?.firstName}}</span>
          </div>
        </div>
      </div>
      <div class="flex flex-col justify-between forum-item-right-content gap-3">
        <button class="flex items-center icons_w lik_hov" [ngClass]="{'is-liked': topic?.liked}"
          (click)="likeTopic(topic); $event.stopPropagation();">
          <div class="icon-wrap like_w">
            <svg class="emty_l" width="24" height="22" viewBox="0 0 24 22" fill="none"
              xmlns="http://www.w3.org/2000/svg">
              <path
                d="M11.7534 2.35249C11.576 2.19912 11.4123 2.05272 11.2486 1.89935C10.048 0.749078 8.90875 0.163484 7.67404 0.0589139C7.44892 0.0379998 7.22381 0.0310285 7.00552 0.0310285C4.87718 0.0310285 3.11038 0.909419 1.72559 2.65923C0.286236 4.47875 -0.252671 6.65381 0.108874 9.12864C0.402203 11.1294 1.2549 12.7816 2.63969 14.0365C4.74757 15.9397 6.91684 17.8428 9.01789 19.6763C9.79555 20.3595 10.5732 21.0357 11.3509 21.7189C11.4668 21.8235 11.5896 21.9002 11.7056 21.942C12.0399 22.0675 12.3468 21.9908 12.6674 21.698C13.8612 20.6453 15.055 19.5926 16.2556 18.54C17.0742 17.815 17.8996 17.0969 18.7182 16.3719C19.0047 16.1209 19.2981 15.8769 19.5777 15.6329C20.2258 15.0822 20.8943 14.5175 21.4946 13.8971C23.6229 11.6802 24.4006 9.03105 23.8071 6.03336C23.3023 3.49579 21.9517 1.69718 19.796 0.679364C17.7291 -0.296625 15.7713 -0.21994 13.9908 0.902448C13.3223 1.32073 12.7016 1.87844 12.1422 2.55466L12.074 2.63831L11.9989 2.5686C11.9171 2.49192 11.8352 2.4222 11.7534 2.35249ZM12.7561 4.47875C12.8175 4.38812 12.8789 4.2975 12.9403 4.20687C13.1586 3.87922 13.3837 3.54459 13.6566 3.25877C15.13 1.69021 17.4426 1.39044 19.4072 2.52677C20.8602 3.36334 21.7538 4.69486 22.0744 6.47953C22.5315 9.0659 21.7675 11.2689 19.7892 13.0187C18.0224 14.5802 16.2215 16.1697 14.4752 17.6964C13.7316 18.3517 12.9881 19.0001 12.2513 19.6554C12.2172 19.6902 12.1763 19.7181 12.1354 19.753L11.9989 19.8645L8.01512 16.3579C7.59218 15.9815 7.15559 15.605 6.73265 15.2425C5.79809 14.4338 4.82943 13.5973 3.90851 12.7328C2.5783 11.485 1.8211 9.77698 1.78699 7.92957C1.75288 6.17976 2.37365 4.51361 3.49921 3.36334C5.02043 1.80872 7.43528 1.39741 9.23618 2.38037C9.94563 2.77077 10.5459 3.35636 11.0576 4.17201C11.0848 4.21384 11.1121 4.25567 11.1326 4.2975C11.1667 4.36024 11.2008 4.41601 11.2417 4.47178C11.4191 4.73669 11.692 4.89703 11.9853 4.89703H11.9921C12.2923 4.904 12.5719 4.75063 12.7561 4.47875Z"
                fill="var(--font-color1)" />
            </svg>
            <svg class="emty_l_hover" width="24" height="22" viewBox="0 0 24 22" fill="none"
              xmlns="http://www.w3.org/2000/svg">
              <path
                d="M11.7534 2.35249C11.576 2.19912 11.4123 2.05272 11.2486 1.89935C10.048 0.749078 8.90875 0.163484 7.67404 0.0589139C7.44892 0.0379998 7.22381 0.0310285 7.00552 0.0310285C4.87718 0.0310285 3.11038 0.909419 1.72559 2.65923C0.286236 4.47875 -0.252671 6.65381 0.108874 9.12864C0.402203 11.1294 1.2549 12.7816 2.63969 14.0365C4.74757 15.9397 6.91684 17.8428 9.01789 19.6763C9.79555 20.3595 10.5732 21.0357 11.3509 21.7189C11.4668 21.8235 11.5896 21.9002 11.7056 21.942C12.0399 22.0675 12.3468 21.9908 12.6674 21.698C13.8612 20.6453 15.055 19.5926 16.2556 18.54C17.0742 17.815 17.8996 17.0969 18.7182 16.3719C19.0047 16.1209 19.2981 15.8769 19.5777 15.6329C20.2258 15.0822 20.8943 14.5175 21.4946 13.8971C23.6229 11.6802 24.4006 9.03105 23.8071 6.03336C23.3023 3.49579 21.9517 1.69718 19.796 0.679364C17.7291 -0.296625 15.7713 -0.21994 13.9908 0.902448C13.3223 1.32073 12.7016 1.87844 12.1422 2.55466L12.074 2.63831L11.9989 2.5686C11.9171 2.49192 11.8352 2.4222 11.7534 2.35249ZM12.7561 4.47875C12.8175 4.38812 12.8789 4.2975 12.9403 4.20687C13.1586 3.87922 13.3837 3.54459 13.6566 3.25877C15.13 1.69021 17.4426 1.39044 19.4072 2.52677C20.8602 3.36334 21.7538 4.69486 22.0744 6.47953C22.5315 9.0659 21.7675 11.2689 19.7892 13.0187C18.0224 14.5802 16.2215 16.1697 14.4752 17.6964C13.7316 18.3517 12.9881 19.0001 12.2513 19.6554C12.2172 19.6902 12.1763 19.7181 12.1354 19.753L11.9989 19.8645L8.01512 16.3579C7.59218 15.9815 7.15559 15.605 6.73265 15.2425C5.79809 14.4338 4.82943 13.5973 3.90851 12.7328C2.5783 11.485 1.8211 9.77698 1.78699 7.92957C1.75288 6.17976 2.37365 4.51361 3.49921 3.36334C5.02043 1.80872 7.43528 1.39741 9.23618 2.38037C9.94563 2.77077 10.5459 3.35636 11.0576 4.17201C11.0848 4.21384 11.1121 4.25567 11.1326 4.2975C11.1667 4.36024 11.2008 4.41601 11.2417 4.47178C11.4191 4.73669 11.692 4.89703 11.9853 4.89703H11.9921C12.2923 4.904 12.5719 4.75063 12.7561 4.47875Z"
                fill="var(--text-color)" />
            </svg>
          </div>
          <span class="ml-2 text-color default_" *ngIf="topic">{{topic?.likes}}</span>
          <div class="on_hov">
            мне нравится
          </div>
        </button>
        <div class="actions_w flex gap-[20px] items-center" style="position: relative">
          <div class="flex items-center cursor-pointer icons_w fav_hov"
            [ngClass]="{'in-favourites': topic.inFavourites}" (click)="favoriteTopic(topic)">
            <div class="icon-wrap star_w">
              <svg class="emty_f" width="25" height="24" viewBox="0 0 25 24" fill="none"
                xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M5.96868 23.9618C5.94253 23.9618 5.91638 23.9618 5.89022 23.9618C5.36715 23.9356 4.89638 23.6999 4.53023 23.3333C4.16408 22.9143 3.98101 22.3906 4.03332 21.8145C4.05947 21.5789 4.11178 21.317 4.19024 21.0552C4.71331 19.4055 5.41946 17.2844 6.1256 15.1372C4.42562 13.9065 2.72564 12.6758 1.02565 11.4189C0.816424 11.2618 0.162584 10.7904 0.0318159 9.95249C-0.0727985 9.35022 0.0841231 8.77414 0.450273 8.35517C0.842577 7.88383 1.44411 7.62197 2.15026 7.62197C2.90871 7.62197 3.66717 7.62197 4.42562 7.62197H8.55789C8.68866 7.25537 8.79327 6.88878 8.92404 6.52218C9.08096 6.05084 9.21173 5.60568 9.36865 5.13435C9.7348 4.00837 10.0748 2.8562 10.4671 1.73022C10.5979 1.31125 10.8333 0.918472 11.1209 0.604245C11.4871 0.211462 12.0363 -0.0242083 12.5855 0.00197721C13.1348 0.0281628 13.6578 0.290018 14.024 0.735173C14.2332 1.02321 14.4163 1.33744 14.5471 1.70404C15.044 3.24899 15.5409 4.79393 16.0378 6.33888L16.4563 7.62197H17.6593C19.3332 7.62197 21.007 7.62197 22.6808 7.62197C23.047 7.62197 23.387 7.67434 23.7008 7.77909C24.2762 7.98857 24.7208 8.43372 24.9039 9.00981C25.087 9.58589 25.0085 10.1882 24.6685 10.7119C24.4854 10.9999 24.25 11.2356 23.9624 11.4451C22.2624 12.6758 20.5624 13.9327 18.8886 15.1372C19.5424 17.1273 20.1962 19.0912 20.8239 21.0813C21.0593 21.7884 21.0332 22.3906 20.7455 22.9405C20.327 23.7785 19.3593 24.1974 18.4701 23.9094C18.1301 23.8046 17.8163 23.6475 17.5286 23.438C16.1424 22.443 14.7563 21.4218 13.344 20.4005L12.5071 19.7983L11.5132 20.5314C10.1794 21.5003 8.84558 22.4954 7.51174 23.4642C6.96252 23.7785 6.4656 23.9618 5.96868 23.9618ZM3.45794 9.14073C3.01333 9.14073 2.59487 9.14073 2.15026 9.14073C1.91487 9.14073 1.7318 9.21929 1.65334 9.32403C1.57488 9.40259 1.54872 9.53352 1.57488 9.66445C1.60103 9.76919 1.65334 9.90012 1.94103 10.1096C3.71947 11.3927 5.47176 12.702 7.25021 13.9851C7.69482 14.3255 7.85174 14.7968 7.69482 15.3205C6.96252 17.5463 6.23022 19.7983 5.68099 21.5003C5.62869 21.6312 5.60253 21.7884 5.60253 21.8931C5.60253 22.0502 5.62869 22.155 5.70715 22.2597C5.75945 22.3383 5.86407 22.3644 5.94253 22.3644C6.0733 22.3644 6.23022 22.3383 6.54406 22.1026C7.87789 21.1337 9.21173 20.1387 10.5456 19.1698L12.4809 17.7558L14.2594 19.0389C15.6455 20.0601 17.0317 21.0552 18.4178 22.0764C18.5747 22.1811 18.7316 22.2597 18.9147 22.3121C19.0716 22.3644 19.2286 22.2859 19.307 22.1288C19.3593 22.024 19.4116 21.8407 19.2809 21.4741C18.6009 19.4055 17.9209 17.3368 17.2409 15.2682C17.0578 14.7183 17.2147 14.2731 17.6593 13.9327C19.4116 12.6758 21.1901 11.3665 22.9424 10.0834C23.0731 9.97867 23.1777 9.87393 23.2824 9.743C23.3608 9.63826 23.387 9.50733 23.3347 9.40259C23.3085 9.29785 23.2039 9.21929 23.0993 9.16692C22.9685 9.11455 22.7854 9.08836 22.6024 9.08836C20.9285 9.08836 19.2547 9.08836 17.5809 9.08836H15.2794L15.0963 8.59084C15.0701 8.51228 15.044 8.45991 15.0178 8.40754L14.4948 6.78404C13.9978 5.23909 13.5009 3.69414 13.004 2.14919C12.9517 1.96589 12.8471 1.78259 12.7425 1.65167C12.664 1.54692 12.5332 1.52074 12.4809 1.52074C12.4286 1.52074 12.324 1.52074 12.2455 1.62548C12.1148 1.75641 12.0102 1.96589 11.9317 2.17538C11.5656 3.30136 11.1994 4.45352 10.8333 5.55331C10.6763 6.02465 10.5456 6.46981 10.3886 6.94115C10.2579 7.30775 10.1533 7.70053 10.0225 8.06713L9.68249 9.14073H4.42562C4.11178 9.14073 3.77178 9.14073 3.45794 9.14073Z"
                  fill="var(--font-color1)" />
              </svg>
              <svg class="emty_f_hover" width="25" height="24" viewBox="0 0 25 24" fill="none"
                xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M5.96868 23.9618C5.94253 23.9618 5.91638 23.9618 5.89022 23.9618C5.36715 23.9356 4.89638 23.6999 4.53023 23.3333C4.16408 22.9143 3.98101 22.3906 4.03332 21.8145C4.05947 21.5789 4.11178 21.317 4.19024 21.0552C4.71331 19.4055 5.41946 17.2844 6.1256 15.1372C4.42562 13.9065 2.72564 12.6758 1.02565 11.4189C0.816424 11.2618 0.162584 10.7904 0.0318159 9.95249C-0.0727985 9.35022 0.0841231 8.77414 0.450273 8.35517C0.842577 7.88383 1.44411 7.62197 2.15026 7.62197C2.90871 7.62197 3.66717 7.62197 4.42562 7.62197H8.55789C8.68866 7.25537 8.79327 6.88878 8.92404 6.52218C9.08096 6.05084 9.21173 5.60568 9.36865 5.13435C9.7348 4.00837 10.0748 2.8562 10.4671 1.73022C10.5979 1.31125 10.8333 0.918472 11.1209 0.604245C11.4871 0.211462 12.0363 -0.0242083 12.5855 0.00197721C13.1348 0.0281628 13.6578 0.290018 14.024 0.735173C14.2332 1.02321 14.4163 1.33744 14.5471 1.70404C15.044 3.24899 15.5409 4.79393 16.0378 6.33888L16.4563 7.62197H17.6593C19.3332 7.62197 21.007 7.62197 22.6808 7.62197C23.047 7.62197 23.387 7.67434 23.7008 7.77909C24.2762 7.98857 24.7208 8.43372 24.9039 9.00981C25.087 9.58589 25.0085 10.1882 24.6685 10.7119C24.4854 10.9999 24.25 11.2356 23.9624 11.4451C22.2624 12.6758 20.5624 13.9327 18.8886 15.1372C19.5424 17.1273 20.1962 19.0912 20.8239 21.0813C21.0593 21.7884 21.0332 22.3906 20.7455 22.9405C20.327 23.7785 19.3593 24.1974 18.4701 23.9094C18.1301 23.8046 17.8163 23.6475 17.5286 23.438C16.1424 22.443 14.7563 21.4218 13.344 20.4005L12.5071 19.7983L11.5132 20.5314C10.1794 21.5003 8.84558 22.4954 7.51174 23.4642C6.96252 23.7785 6.4656 23.9618 5.96868 23.9618ZM3.45794 9.14073C3.01333 9.14073 2.59487 9.14073 2.15026 9.14073C1.91487 9.14073 1.7318 9.21929 1.65334 9.32403C1.57488 9.40259 1.54872 9.53352 1.57488 9.66445C1.60103 9.76919 1.65334 9.90012 1.94103 10.1096C3.71947 11.3927 5.47176 12.702 7.25021 13.9851C7.69482 14.3255 7.85174 14.7968 7.69482 15.3205C6.96252 17.5463 6.23022 19.7983 5.68099 21.5003C5.62869 21.6312 5.60253 21.7884 5.60253 21.8931C5.60253 22.0502 5.62869 22.155 5.70715 22.2597C5.75945 22.3383 5.86407 22.3644 5.94253 22.3644C6.0733 22.3644 6.23022 22.3383 6.54406 22.1026C7.87789 21.1337 9.21173 20.1387 10.5456 19.1698L12.4809 17.7558L14.2594 19.0389C15.6455 20.0601 17.0317 21.0552 18.4178 22.0764C18.5747 22.1811 18.7316 22.2597 18.9147 22.3121C19.0716 22.3644 19.2286 22.2859 19.307 22.1288C19.3593 22.024 19.4116 21.8407 19.2809 21.4741C18.6009 19.4055 17.9209 17.3368 17.2409 15.2682C17.0578 14.7183 17.2147 14.2731 17.6593 13.9327C19.4116 12.6758 21.1901 11.3665 22.9424 10.0834C23.0731 9.97867 23.1777 9.87393 23.2824 9.743C23.3608 9.63826 23.387 9.50733 23.3347 9.40259C23.3085 9.29785 23.2039 9.21929 23.0993 9.16692C22.9685 9.11455 22.7854 9.08836 22.6024 9.08836C20.9285 9.08836 19.2547 9.08836 17.5809 9.08836H15.2794L15.0963 8.59084C15.0701 8.51228 15.044 8.45991 15.0178 8.40754L14.4948 6.78404C13.9978 5.23909 13.5009 3.69414 13.004 2.14919C12.9517 1.96589 12.8471 1.78259 12.7425 1.65167C12.664 1.54692 12.5332 1.52074 12.4809 1.52074C12.4286 1.52074 12.324 1.52074 12.2455 1.62548C12.1148 1.75641 12.0102 1.96589 11.9317 2.17538C11.5656 3.30136 11.1994 4.45352 10.8333 5.55331C10.6763 6.02465 10.5456 6.46981 10.3886 6.94115C10.2579 7.30775 10.1533 7.70053 10.0225 8.06713L9.68249 9.14073H4.42562C4.11178 9.14073 3.77178 9.14073 3.45794 9.14073Z"
                  fill="var(--text-color)" />
              </svg>
            </div>
            <p class="show_p_md">
              добавить в избранное
            </p>
            <div class="on_hov">
              добавить в избранное
            </div>
          </div>
          <div (click)="copyTopic(topic?.topicComment ? topic?.topicComment?.topic.id : topic?.topic?.id)"
            class="flex items-center cursor-pointer icons_w shr_hov" (click)="share(topic)">
            <div class="icon-wrap share_w">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M15.9238 7.01009C13.5066 8.21941 11.0965 9.42157 8.66504 10.638C8.9368 11.5325 8.94395 12.427 8.66504 13.3357C11.0822 14.5451 13.4923 15.7544 15.8737 16.9422C16.2456 16.5988 16.5745 16.2267 16.9607 15.9476C19.6353 13.9941 23.4542 15.6113 23.9334 18.8957C24.3481 21.7509 22.0168 24.2268 19.1347 23.9835C17.0894 23.8117 15.3731 22.1516 15.1514 20.1051C15.1085 19.6829 15.1156 19.2464 15.1729 18.8313C15.2086 18.588 15.1442 18.5093 14.9512 18.4163C12.8772 17.3859 10.8105 16.3483 8.73656 15.3107C8.50056 15.1891 8.25741 15.0817 8.02141 14.9458C7.87838 14.8671 7.79972 14.8885 7.6853 15.0102C6.76276 15.9834 5.63283 16.4556 4.28836 16.4413C2.21445 16.4127 0.326467 14.7454 0.0475605 12.6846C-0.288557 10.2588 1.18464 8.12638 3.58752 7.63979C5.16084 7.32494 6.52676 7.79722 7.65669 8.94213C7.80687 9.09241 7.89984 9.10672 8.07862 9.01369C10.3742 7.85447 12.677 6.70955 14.9798 5.56464C15.1585 5.47877 15.2158 5.40005 15.1729 5.18538C14.7295 2.75959 16.4458 0.39105 18.8773 0.047575C21.4232 -0.317367 23.7117 1.45725 23.9763 3.99753C24.2266 6.37323 22.4673 8.57004 20.093 8.8348C18.4196 9.028 17.0751 8.41977 16.0239 7.12458C15.9881 7.09596 15.9595 7.06018 15.9238 7.01009ZM7.14179 11.9976C7.14179 10.5021 5.91174 9.26414 4.42424 9.2713C2.94389 9.27845 1.721 10.4949 1.70669 11.9762C1.69239 13.4574 2.92244 14.7025 4.41709 14.7168C5.90459 14.7311 7.14179 13.5003 7.14179 11.9976ZM16.832 19.5541C16.8248 21.0496 18.0549 22.2876 19.5424 22.2804C21.0227 22.2804 22.2671 21.0353 22.2671 19.5541C22.2599 18.0728 21.0513 16.8635 19.5567 16.8492C18.062 16.8277 16.8391 18.0442 16.832 19.5541ZM19.5424 7.14605C21.037 7.14605 22.2671 5.91527 22.2671 4.42688C22.2599 2.93849 21.0156 1.70055 19.5352 1.7077C18.0477 1.71486 16.8463 2.93133 16.832 4.41972C16.8248 5.92242 18.0406 7.14605 19.5424 7.14605Z"
                  fill="var(--font-color1)" />
              </svg>
            </div>
            <div class="on_hov">
              поделиться
            </div>
          </div>
        </div>
        <div class="forum-answers-count flex justify-end">
          <span *ngIf="topic?.topic?.comments">{{topic?.topic?.comments.length}} ответ</span>
        </div>
      </div>
    </div>
    <div class="mobile-actions icons_w" (mouseleave)="closeMobileActionSelect()">
      <div class="icon-wrap star_w" (click)="showMobileActionOptions(topic)">
        <svg class="emty_f" width="7" height="22" viewBox="0 0 7 22" fill="none" xmlns="http://www.w3.org/2000/svg">
          <circle cx="3.27778" cy="3.27778" r="2.27778" stroke="var(--font-color1)" />
          <circle cx="3.27778" cy="11.2499" r="2.27778" stroke="var(--font-color1)" />
          <circle cx="3.27778" cy="19.2221" r="2.27778" stroke="var(--font-color1)" />
        </svg>
        <svg class="emty_f_hover" width="7" height="22" viewBox="0 0 7 22" fill="none"
          xmlns="http://www.w3.org/2000/svg">
          <circle cx="3.27778" cy="3.27778" r="2.27778" fill="var(--text-color)" stroke="var(--text-color)" />
          <circle cx="3.27778" cy="11.2499" r="2.27778" fill="var(--text-color)" stroke="var(--text-color)" />
          <circle cx="3.27778" cy="19.2221" r="2.27778" fill="var(--text-color)" stroke="var(--text-color)" />
        </svg>
      </div>
      @if (selectedDropdElement?.id === topic?.id) {
      <div class="dropdown-content-wrapper">
        <div class="dropdown-content">
          <div class="dropdown-content-inner">
            @for(action of forumActions; track action) {
            <div class="dropdown-item flex gap-2 items-center" (click)="onClickMobileAction()">
              <ng-container *ngTemplateOutlet="dropdItemIcon; context: {action: action, item: topic}"></ng-container>
              {{action}}
            </div>
            }
          </div>
        </div>
      </div>
      }
    </div>
  </div>
</ng-template>

<ng-template #dropdItemIcon let-action="action" let-item="item">
  <ng-container [ngSwitch]="action">
    <div *ngSwitchCase="'копировать'">
      <svg width="16" height="15" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
          d="M8.4248 5.16141C6.49927 5.16141 4.57374 5.15626 2.64821 5.16141C1.36109 5.16655 0.336546 5.97446 0.0636768 7.20434C0.0173397 7.41017 0.0018959 7.6263 0.0018959 7.84243C-0.00325394 11.6658 -0.00325394 15.4944 0.0018959 19.3178C0.0018959 20.8822 1.12426 22.004 2.68425 22.004C6.50957 22.004 10.34 22.004 14.1653 22.004C15.715 22.004 16.8477 20.8873 16.8477 19.3384C16.8528 15.5047 16.8528 11.671 16.8477 7.83728C16.8477 6.27292 15.7253 5.16141 14.1499 5.16141C12.245 5.15626 10.3349 5.16141 8.4248 5.16141ZM15.123 13.5801C15.123 15.479 15.123 17.383 15.123 19.2818C15.123 19.9559 14.8038 20.275 14.1293 20.275C10.3246 20.275 6.52501 20.275 2.72029 20.275C2.04584 20.275 1.72663 19.9559 1.72663 19.2767C1.72663 15.4738 1.72663 11.6761 1.72663 7.8733C1.72663 7.19919 2.04584 6.88014 2.72544 6.88014C6.53016 6.88014 10.3297 6.88014 14.1345 6.88014C14.8141 6.88014 15.1281 7.19919 15.1281 7.87845C15.123 9.78244 15.123 11.6813 15.123 13.5801Z"
          fill="var(--font-color1)" />
        <path
          d="M5.18795 6.05114C5.74398 6.05114 6.28972 6.05114 6.87665 6.05114C6.87665 5.82471 6.87665 2.94349 6.87665 2.72221C6.87665 2.07383 7.21644 1.72905 7.86 1.72905C11.6699 1.72905 15.4849 1.72905 19.2948 1.72905C19.9435 1.7239 20.2884 2.06353 20.2884 2.70677C20.2884 6.51475 20.2884 10.3279 20.2884 14.1359C20.2884 14.7842 19.9435 15.129 19.2999 15.129C19.0631 15.129 16.7555 15.129 16.5033 15.129C16.5033 15.7002 16.5033 16.2508 16.5033 16.8066C16.5555 16.8147 18.6783 16.8219 18.7299 16.828C20.348 17.0213 21.6675 16.2614 21.9668 14.6607C21.9926 14.5269 21.9977 14.388 21.9977 14.2491C21.9977 10.369 22.0029 6.49417 21.9977 2.61415C21.9977 1.2865 21.1122 0.267609 19.8148 0.0411882C19.65 0.0154586 19.4801 0.0103127 19.3102 0.0103127C15.49 0.0103127 11.6699 0.0206045 7.85486 2.08741e-05C6.48021 -0.00512504 5.363 0.941724 5.1931 2.21791C5.14676 2.59871 5.18795 5.63946 5.18795 6.05114Z"
          fill="var(--font-color1)" />
      </svg>
    </div>
    <div *ngSwitchCase="'удалить'">
      <svg class="emty_f" width="16" height="16" viewBox="0 0 18 22" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
          d="M10.6234 0C10.7094 0.0286963 10.7955 0.0573925 10.8816 0.0803496C11.7655 0.338616 12.3624 1.11342 12.3796 2.0317C12.3853 2.26127 12.3796 2.49084 12.3796 2.7491C12.4657 2.7491 12.5403 2.7491 12.6149 2.7491C13.7685 2.7491 14.9221 2.7491 16.0699 2.7491C17.1432 2.7491 17.872 3.48373 17.872 4.55697C17.872 5.29159 17.872 6.03196 17.872 6.76658C17.872 7.28885 17.608 7.55286 17.0743 7.5586C16.9882 7.5586 16.9078 7.5586 16.8103 7.5586C16.7758 8.24731 16.7471 8.90732 16.7127 9.57308C16.6037 11.863 16.4946 14.1473 16.3856 16.4372C16.3282 17.6654 16.2823 18.8994 16.2019 20.1276C16.133 21.1664 15.2607 21.9699 14.2219 21.9928C13.7398 22.0043 13.2634 21.9986 12.7813 21.9986C9.79117 21.9986 6.79528 21.9986 3.80513 21.9986C2.72041 21.9986 1.85952 21.3213 1.69308 20.2997C1.62421 19.8578 1.62421 19.3987 1.60125 18.951C1.49221 16.7012 1.3889 14.4457 1.28559 12.1959C1.21672 10.6865 1.14211 9.17707 1.0675 7.66764C1.0675 7.63895 1.05602 7.61599 1.05028 7.56434C0.92402 7.56434 0.809235 7.57008 0.688711 7.56434C0.298441 7.55286 0.00573925 7.27164 0 6.88137C0.00573925 6.04917 0 5.21698 0.0172178 4.39053C0.0344355 3.48947 0.792017 2.76058 1.7103 2.75484C2.89258 2.74336 4.07487 2.75484 5.25142 2.75484C5.32603 2.75484 5.40638 2.75484 5.50968 2.75484C5.50968 2.54249 5.50968 2.34162 5.50968 2.14648C5.51542 1.06176 6.14674 0.275484 7.19703 0.0344355C7.22572 0.0286963 7.24868 0.0114785 7.27164 0C8.39079 0 9.50421 0 10.6234 0ZM2.47362 7.57008C2.47362 7.77095 2.46788 7.94887 2.47362 8.12679C2.58266 10.4225 2.69171 12.7182 2.80076 15.0082C2.87537 16.6151 2.94998 18.2279 3.03033 19.8349C3.05902 20.3858 3.31155 20.6154 3.86252 20.6154C7.24868 20.6154 10.6348 20.6154 14.021 20.6154C14.5834 20.6154 14.8302 20.3858 14.8589 19.8176C14.9508 17.8893 15.0426 15.9551 15.1344 14.0267C15.2148 12.3624 15.2951 10.698 15.3755 9.02785C15.3984 8.54575 15.4214 8.05791 15.4386 7.56434C11.1055 7.57008 6.79528 7.57008 2.47362 7.57008ZM16.5061 6.18118C16.5061 5.65891 16.5061 5.15959 16.5061 4.66027C16.5061 4.21835 16.42 4.13226 15.9838 4.13226C11.2891 4.13226 6.5944 4.13226 1.89969 4.13226C1.85952 4.13226 1.8136 4.13226 1.77343 4.13226C1.5209 4.138 1.3889 4.26427 1.3889 4.51679C1.38316 5.02759 1.3889 5.53264 1.3889 6.04344C1.3889 6.08935 1.40038 6.13526 1.40612 6.18692C6.43944 6.18118 11.4613 6.18118 16.5061 6.18118ZM11.0079 2.74336C11.0079 2.4851 11.0194 2.24979 11.0079 2.01448C10.9907 1.66438 10.7037 1.38316 10.3479 1.37742C9.41812 1.37168 8.48836 1.37168 7.5586 1.37742C7.23146 1.37742 6.93876 1.62995 6.90432 1.95135C6.87563 2.20961 6.89858 2.47362 6.89858 2.74336C8.27027 2.74336 9.62473 2.74336 11.0079 2.74336Z"
          fill="var(--font-color1)" />
        <path
          d="M6.20236 14.1013C6.20236 15.5763 6.20236 17.0513 6.20236 18.5263C6.20236 19.0371 5.73748 19.3757 5.28408 19.2035C5.01433 19.1002 4.83642 18.8592 4.83068 18.5607C4.82494 18.2451 4.83068 17.9294 4.83068 17.6138C4.83068 14.9737 4.83068 12.3279 4.83068 9.68784C4.83068 9.14262 5.28982 8.79826 5.76043 8.98192C6.0474 9.09096 6.20236 9.34349 6.20236 9.69932C6.20236 11.1628 6.20236 12.6321 6.20236 14.1013Z"
          fill="var(--font-color1)" />
        <path
          d="M9.63986 14.0841C9.63986 15.5591 9.63986 17.0341 9.63986 18.5091C9.63986 19.0371 9.17498 19.3815 8.71584 19.2036C8.44035 19.1003 8.26818 18.8535 8.26818 18.5378C8.26244 17.9811 8.26818 17.4187 8.26818 16.8619C8.26818 14.4687 8.26818 12.0811 8.26818 9.68788C8.26818 9.14839 8.72732 8.80403 9.20367 8.98769C9.47916 9.09674 9.63986 9.34352 9.63986 9.68214C9.63986 11.0136 9.63986 12.3452 9.63986 13.6767C9.63986 13.8087 9.63986 13.9464 9.63986 14.0841Z"
          fill="var(--font-color1)" />
        <path
          d="M13.0674 14.1128C13.0674 15.5821 13.0674 17.0456 13.0674 18.5148C13.0674 18.8247 12.9354 19.0543 12.6542 19.1863C12.3844 19.3068 12.1376 19.2552 11.9195 19.06C11.7474 18.9051 11.69 18.7099 11.6957 18.4804C11.7014 17.2866 11.6957 16.0871 11.6957 14.8933C11.6957 13.1543 11.6957 11.4154 11.6957 9.67636C11.6957 9.13687 12.1721 8.79825 12.6369 8.98765C12.9124 9.09669 13.0674 9.34922 13.0674 9.69358C13.0731 11.1628 13.0674 12.6378 13.0674 14.1128Z"
          fill="var(--font-color1)" />
      </svg>
    </div>
    <div *ngSwitchCase="'поделиться'">
      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
          d="M15.9238 7.01009C13.5066 8.21941 11.0965 9.42157 8.66504 10.638C8.9368 11.5325 8.94395 12.427 8.66504 13.3357C11.0822 14.5451 13.4923 15.7544 15.8737 16.9422C16.2456 16.5988 16.5745 16.2267 16.9607 15.9476C19.6353 13.9941 23.4542 15.6113 23.9334 18.8957C24.3481 21.7509 22.0168 24.2268 19.1347 23.9835C17.0894 23.8117 15.3731 22.1516 15.1514 20.1051C15.1085 19.6829 15.1156 19.2464 15.1729 18.8313C15.2086 18.588 15.1442 18.5093 14.9512 18.4163C12.8772 17.3859 10.8105 16.3483 8.73656 15.3107C8.50056 15.1891 8.25741 15.0817 8.02141 14.9458C7.87838 14.8671 7.79972 14.8885 7.6853 15.0102C6.76276 15.9834 5.63283 16.4556 4.28836 16.4413C2.21445 16.4127 0.326467 14.7454 0.0475605 12.6846C-0.288557 10.2588 1.18464 8.12638 3.58752 7.63979C5.16084 7.32494 6.52676 7.79722 7.65669 8.94213C7.80687 9.09241 7.89984 9.10672 8.07862 9.01369C10.3742 7.85447 12.677 6.70955 14.9798 5.56464C15.1585 5.47877 15.2158 5.40005 15.1729 5.18538C14.7295 2.75959 16.4458 0.39105 18.8773 0.047575C21.4232 -0.317367 23.7117 1.45725 23.9763 3.99753C24.2266 6.37323 22.4673 8.57004 20.093 8.8348C18.4196 9.028 17.0751 8.41977 16.0239 7.12458C15.9881 7.09596 15.9595 7.06018 15.9238 7.01009ZM7.14179 11.9976C7.14179 10.5021 5.91174 9.26414 4.42424 9.2713C2.94389 9.27845 1.721 10.4949 1.70669 11.9762C1.69239 13.4574 2.92244 14.7025 4.41709 14.7168C5.90459 14.7311 7.14179 13.5003 7.14179 11.9976ZM16.832 19.5541C16.8248 21.0496 18.0549 22.2876 19.5424 22.2804C21.0227 22.2804 22.2671 21.0353 22.2671 19.5541C22.2599 18.0728 21.0513 16.8635 19.5567 16.8492C18.062 16.8277 16.8391 18.0442 16.832 19.5541ZM19.5424 7.14605C21.037 7.14605 22.2671 5.91527 22.2671 4.42688C22.2599 2.93849 21.0156 1.70055 19.5352 1.7077C18.0477 1.71486 16.8463 2.93133 16.832 4.41972C16.8248 5.92242 18.0406 7.14605 19.5424 7.14605Z"
          fill="var(--font-color1)" />
      </svg>
    </div>
    <div *ngSwitchCase="'мне нравится'">
      <div class="flex items-center icons_w lik_hov" [ngClass]="{'is-liked': item.liked}">
        <div class="icon-wrap like_w">
          <svg width="16" height="15" viewBox="0 0 24 22" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M11.7534 2.35249C11.576 2.19912 11.4123 2.05272 11.2486 1.89935C10.048 0.749078 8.90875 0.163484 7.67404 0.0589139C7.44892 0.0379998 7.22381 0.0310285 7.00552 0.0310285C4.87718 0.0310285 3.11038 0.909419 1.72559 2.65923C0.286236 4.47875 -0.252671 6.65381 0.108874 9.12864C0.402203 11.1294 1.2549 12.7816 2.63969 14.0365C4.74757 15.9397 6.91684 17.8428 9.01789 19.6763C9.79555 20.3595 10.5732 21.0357 11.3509 21.7189C11.4668 21.8235 11.5896 21.9002 11.7056 21.942C12.0399 22.0675 12.3468 21.9908 12.6674 21.698C13.8612 20.6453 15.055 19.5926 16.2556 18.54C17.0742 17.815 17.8996 17.0969 18.7182 16.3719C19.0047 16.1209 19.2981 15.8769 19.5777 15.6329C20.2258 15.0822 20.8943 14.5175 21.4946 13.8971C23.6229 11.6802 24.4006 9.03105 23.8071 6.03336C23.3023 3.49579 21.9517 1.69718 19.796 0.679364C17.7291 -0.296625 15.7713 -0.21994 13.9908 0.902448C13.3223 1.32073 12.7016 1.87844 12.1422 2.55466L12.074 2.63831L11.9989 2.5686C11.9171 2.49192 11.8352 2.4222 11.7534 2.35249ZM12.7561 4.47875C12.8175 4.38812 12.8789 4.2975 12.9403 4.20687C13.1586 3.87922 13.3837 3.54459 13.6566 3.25877C15.13 1.69021 17.4426 1.39044 19.4072 2.52677C20.8602 3.36334 21.7538 4.69486 22.0744 6.47953C22.5315 9.0659 21.7675 11.2689 19.7892 13.0187C18.0224 14.5802 16.2215 16.1697 14.4752 17.6964C13.7316 18.3517 12.9881 19.0001 12.2513 19.6554C12.2172 19.6902 12.1763 19.7181 12.1354 19.753L11.9989 19.8645L8.01512 16.3579C7.59218 15.9815 7.15559 15.605 6.73265 15.2425C5.79809 14.4338 4.82943 13.5973 3.90851 12.7328C2.5783 11.485 1.8211 9.77698 1.78699 7.92957C1.75288 6.17976 2.37365 4.51361 3.49921 3.36334C5.02043 1.80872 7.43528 1.39741 9.23618 2.38037C9.94563 2.77077 10.5459 3.35636 11.0576 4.17201C11.0848 4.21384 11.1121 4.25567 11.1326 4.2975C11.1667 4.36024 11.2008 4.41601 11.2417 4.47178C11.4191 4.73669 11.692 4.89703 11.9853 4.89703H11.9921C12.2923 4.904 12.5719 4.75063 12.7561 4.47875Z"
              fill="var(--font-color1)" />
          </svg>
        </div>

      </div>
    </div>

  </ng-container>
</ng-template>
<playlist-dialog [showPlaylist]="showPlaylist" [selectedTrackId]="selectedTrackId"
  (playlistClosed)="playlistClosed($event)" (playlistSelected)="playlistSelected($event)">
</playlist-dialog>